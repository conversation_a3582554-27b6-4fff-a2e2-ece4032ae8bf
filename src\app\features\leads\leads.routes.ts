import { Routes } from '@angular/router';
import { AutoDialerComponent } from 'src/app/features/leads/auto-dialer/auto-dialer.component';
import { CustomLeadFormComponent } from 'src/app/features/leads/custom-lead-form/custom-lead-form.component';
import { LeadNameSectionComponent } from 'src/app/features/leads/lead-name-section/lead-name-section.component';
import { LeadsAdvanceFilterComponent } from 'src/app/features/leads/leads-advance-filter/leads-advance-filter.component';
import { LeadsRootLayoutComponent } from 'src/app/features/leads/leads-root-component';
import { ManageLeadsComponent } from 'src/app/features/leads/manage-leads.component';
import { BulkUploadComponent } from 'src/app/shared/components/bulk-upload/bulk-upload.component';
import { CustomizationAddLeadComponent } from './customization-add-lead/customization-add-lead.component';

export const routes: Routes = [
  {
    path: '',
    component: LeadsRootLayoutComponent,
    children: [
      { path: '', redirectTo: 'manage-leads', pathMatch: 'full' },
      { path: 'manage-leads', component: ManageLeadsComponent },
      {
        path: 'add-lead',
        component: CustomizationAddLeadComponent,
      },
      {
        path: 'edit-lead/:id',
        component: CustomizationAddLeadComponent,
      },
      { path: 'bulk-upload', component: BulkUploadComponent },
      { path: 'auto-dialer', component: AutoDialerComponent },
    ],
  },
];

export const LEADS_DECLARATIONS = [
  LeadsRootLayoutComponent,
  CustomLeadFormComponent,
  LeadNameSectionComponent,
  ManageLeadsComponent,
  LeadsAdvanceFilterComponent,
  AutoDialerComponent,
];
