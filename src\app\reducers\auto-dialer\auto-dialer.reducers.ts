import { Action, createSelector } from "@ngrx/store";
import { AppState } from "src/app/app.reducer";
import { AutoDialerActionTypes, FetchAutoDialerSuccess, FetchStatusCountSuccess, GetUserStatusSuccess } from "./auto-dialer.actions";

export type AutoDialerState = {
    autoDialer: any;
    statusCount: any;
    userStatus: any;
    autoDialerIsLoading: boolean;
    filtersPayload: any;
    totalCount: number;
    isStatusCountLoading: boolean;
};

const initialState: AutoDialerState = {
    autoDialer: [],
    statusCount: [],
    userStatus: null,
    autoDialerIsLoading: true,
    filtersPayload: {
        pageNumber: 1,
        pageSize: 10,
        SearchByNameAndNumber: '',
        filterType: 'all',
    },
    totalCount: 0,
    isStatusCountLoading: true,
};

export function autoDialerReducer(
    state: AutoDialerState = initialState,
    action: Action
): AutoDialerState {
    switch (action.type) {
        case AutoDialerActionTypes.FETCH_AUTO_DIALER:
            return {
                ...state,
                autoDialerIsLoading: true,
            };
        case AutoDialerActionTypes.FETCH_AUTO_DIALER_SUCCESS:
            return {
                ...state,
                autoDialer: (action as FetchAutoDialerSuccess).response?.items,
                totalCount: (action as FetchAutoDialerSuccess).response?.totalCount || 0,
                autoDialerIsLoading: false
            };
        case AutoDialerActionTypes.FETCH_STATUS_COUNT:
            return {
                ...state,
                isStatusCountLoading: true,
            };
        case AutoDialerActionTypes.FETCH_STATUS_COUNT_SUCCESS:
            return {
                ...state,
                statusCount: (action as FetchStatusCountSuccess).response,
                isStatusCountLoading: false,
            };
        case AutoDialerActionTypes.GET_USER_STATUS_SUCCESS:
            return {
                ...state,
                userStatus: (action as GetUserStatusSuccess).response,
            };
        case AutoDialerActionTypes.UPDATE_FILTER_PAYLOAD:
            return {
                ...state,
                filtersPayload: (action as any).filter,
            };
        default:
            return state;
    }
}

export const selectFeature = (state: AppState) => state.autoDialer;

export const getAutoDialer = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.autoDialer
);

export const getAutoDialerIsLoading = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.autoDialerIsLoading
);

export const getStatusCount = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.statusCount
);

export const getStatusCountIsLoading = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.isStatusCountLoading
);

export const getUserStatus = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.userStatus
);

export const getFiltersPayload = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.filtersPayload
);

export const getTotalCount = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.totalCount
);
