# Dialer SignalR Implementation

## Overview

This implementation extends the existing SignalR service to support both chat-hub and dialer-hub connections without affecting the existing chat functionality.

## Key Features

- **Dual Hub Support**: Maintains separate connections for chat-hub and dialer-hub
- **Backward Compatibility**: All existing chat functionality remains unchanged
- **Real-time Updates**: Supports real-time call status updates, lead assignments, and user status changes
- **Event-driven Architecture**: Uses typed events for better type safety

## Architecture

### SignalR Service Updates

The `SignalRService` now supports:

1. **Chat Hub Connection** (`chat-hub`) - Existing functionality
2. **Dialer Hub Connection** (`dialer-hub`) - New functionality

### New Interfaces

```typescript
export interface DialerEventDto {
  leadId?: string;
  userId?: string;
  tenantId?: string;
  callStatus?: IVRCallStatus;
  callDuration?: number;
  phoneNumber?: string;
  eventType?: string;
  timestamp?: Date;
  message?: string;
  data?: any;
}
```

## Usage

### Starting Connections

```typescript
// Start chat connection (existing functionality)
this.signalRService.startConnection(); // or startChatConnection()

// Start dialer connection (new functionality)
this.signalRService.startDialerConnection();
```

### Listening to Events

```typescript
// Chat events (existing)
this.signalRService.getMessageListener().subscribe((message: WAWrapperDto) => {
  // Handle chat messages
});

// Dialer events (new)
this.signalRService.getDialerEventListener().subscribe((event: DialerEventDto) => {
  // Handle dialer events
});
```

### Sending Events

```typescript
// Send chat message (existing)
this.signalRService.sendMessageToGroup(groupName, waWrapperDto);

// Send dialer event (new)
this.signalRService.sendDialerEventToGroup(groupName, dialerEvent);

// Update call status (convenience method)
this.signalRService.updateCallStatus(leadId, IVRCallStatus.Connected, 120);

// Notify user status change (convenience method)
this.signalRService.notifyUserStatusChange(userId, 'Available', additionalData);
```

## Auto-Dialer Component Integration

The auto-dialer component now includes:

1. **Real-time Call Status Updates**: Call status changes are broadcast to all connected clients
2. **Lead Assignment Notifications**: When leads are assigned, all relevant users are notified
3. **User Status Synchronization**: User availability status is synchronized across clients

### Event Types Supported

- `CallStatusUpdate`: When call status changes (Initiated, Ringing, Connected, etc.)
- `LeadAssigned`: When a lead is assigned to a user
- `UserStatusChange`: When user availability status changes

## Implementation Details

### Auto-Dialer Component Changes

1. **Constructor**: Added `SignalRService` dependency
2. **ngOnInit**: Initializes dialer SignalR connection and event listeners
3. **Call Methods**: Updated to send real-time status updates
4. **Event Handlers**: Added handlers for incoming dialer events
5. **ngOnDestroy**: Properly closes dialer connection

### Backward Compatibility

- All existing chat functionality remains unchanged
- Existing method signatures are preserved
- Legacy methods are maintained for compatibility
- No breaking changes to existing code

## Configuration

The dialer hub uses the same base URL as the chat hub but connects to `dialer-hub` endpoint:

```typescript
// Chat hub URL: ${whatsAppBaseURL}chat-hub
// Dialer hub URL: ${whatsAppBaseURL}dialer-hub
```

## Error Handling

- Connection failures are logged with specific hub identification
- Automatic reconnection is enabled for both hubs
- Graceful degradation when SignalR is unavailable

## Testing

To test the implementation:

1. Open multiple browser tabs with the auto-dialer component
2. Initiate a call in one tab
3. Observe real-time status updates in other tabs
4. Verify chat functionality remains unaffected

## Future Enhancements

- Add more granular call events (ringing, answered, transferred)
- Implement call queue management
- Add real-time analytics and reporting
- Support for multiple concurrent calls per user
