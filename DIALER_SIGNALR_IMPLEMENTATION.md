# Dialer SignalR Implementation - Real-time Call Widget

## Overview

This implementation extends the existing SignalR service to support real-time call status updates in the dialer widget. The focus is on showing live call information for the current screen only.

## Key Features

- **Real-time Call Status**: Shows live call status updates in the floating dialer widget
- **Live Call Duration**: Real-time timer showing current call duration
- **Visual Indicators**: Color-coded status indicators and pulsing animations
- **Current Screen Only**: Updates only affect the current user's screen/session
- **Chat Compatibility**: Existing chat functionality remains completely unchanged

## Architecture

### SignalR Service Updates

The `SignalRService` now supports:

1. **Chat Hub Connection** (`chat-hub`) - Existing functionality
2. **Dialer Hub Connection** (`dialer-hub`) - New functionality

### New Interfaces

```typescript
export interface DialerEventDto {
  leadId?: string;
  userId?: string;
  tenantId?: string;
  callStatus?: IVRCallStatus;
  callDuration?: number;
  phoneNumber?: string;
  eventType?: string;
  timestamp?: Date;
  message?: string;
  data?: any;
}
```

## Usage

### Starting Connections

```typescript
// Start chat connection (existing functionality)
this.signalRService.startConnection(); // or startChatConnection()

// Start dialer connection (new functionality)
this.signalRService.startDialerConnection();
```

### Listening to Events

```typescript
// Chat events (existing)
this.signalRService.getMessageListener().subscribe((message: WAWrapperDto) => {
  // Handle chat messages
});

// Dialer events (new)
this.signalRService.getDialerEventListener().subscribe((event: DialerEventDto) => {
  // Handle dialer events
});
```

### Sending Events

```typescript
// Send chat message (existing)
this.signalRService.sendMessageToGroup(groupName, waWrapperDto);

// Send dialer event (unified method)
this.signalRService.sendDialerEvent(
  loginUserId,               // Current logged-in user ID (required)
  leadId,                    // Lead ID
  IVRCallStatus.Connected,   // Call status
  120,                       // Call duration in seconds
  'CallStatusUpdate',        // Event type
  phoneNumber,               // Phone number (optional)
  'Call connected',          // Message (optional)
  additionalData             // Extra data (optional)
);
```

## Dialer Widget Features

The floating dialer widget now displays:

### Real-time Call Information
- **Call Status**: Shows current call status (Ready, Initiated, Ringing, Connected, etc.)
- **Call Duration**: Live timer showing elapsed call time (MM:SS format)
- **Visual Indicators**: Color-coded phone icon based on call status
- **Pulsing Animation**: Phone icon pulses during active calls

### Status Color Coding
- **Green**: Ready to call / Call completed
- **Yellow/Warning**: Call initiated or ringing
- **Blue/Info**: Call connected or in progress
- **Red/Danger**: Call failed or dropped

### Widget Controls
- **Call Button**: Start a new call (visible when not dialing)
- **Skip Button**: Skip current lead (visible when not dialing)
- **End Call Button**: End current call (visible when dialing)

### Real-time Updates
- Call status changes are immediately reflected in the widget
- Call duration updates every second during active calls
- Visual indicators change based on call status
- Only affects the current user's screen

## Usage Examples

### Basic Call Status Updates

```typescript
// Start a call
this.signalRService.sendDialerEvent(
  'user-123',              // loginUserId
  'lead-123',              // leadId
  IVRCallStatus.Initiated,
  0,
  'CallStatusUpdate'
);

// Call connected
this.signalRService.sendDialerEvent(
  'user-123',              // loginUserId
  'lead-123',              // leadId
  IVRCallStatus.Connected,
  15,
  'CallStatusUpdate'
);

// End call
this.signalRService.sendDialerEvent(
  'user-123',              // loginUserId
  'lead-123',              // leadId
  IVRCallStatus.Completed,
  180,
  'CallStatusUpdate'
);
```

### Event Handling in Component

```typescript
// Listen for dialer events
this.signalRService.getDialerEventListener().subscribe(event => {
  if (this.currentLead && event.leadId === this.currentLead.id) {
    // Update widget display for current lead only
    this.updateCallStatus(event);
  }
});
```

## Implementation Details

### Auto-Dialer Component Changes

1. **Constructor**: Added `SignalRService` dependency
2. **ngOnInit**: Initializes dialer SignalR connection and event listeners
3. **Call Methods**: Updated to send real-time status updates
4. **Event Handlers**: Added handlers for incoming dialer events
5. **ngOnDestroy**: Properly closes dialer connection

### Backward Compatibility

- All existing chat functionality remains unchanged
- Existing method signatures are preserved
- Legacy methods are maintained for compatibility
- No breaking changes to existing code

## Configuration

The dialer hub uses the same base URL as the chat hub but connects to `dialer-hub` endpoint:

```typescript
// Chat hub URL: ${whatsAppBaseURL}chat-hub
// Dialer hub URL: ${whatsAppBaseURL}dialer-hub
```

## Error Handling

- Connection failures are logged with specific hub identification
- Automatic reconnection is enabled for both hubs
- Graceful degradation when SignalR is unavailable

## Testing

To test the implementation:

1. Open multiple browser tabs with the auto-dialer component
2. Initiate a call in one tab
3. Observe real-time status updates in other tabs
4. Verify chat functionality remains unaffected

## Backend Requirements

### **Frontend Sends to Backend (AutoDialerLeadDto):**
```typescript
// Call started
this.signalRService.sendDialerEvent(
  loginUserId,           // Current logged-in user ID
  leadId,               // Lead ID
  new Date(),           // Call start time
  leadName,             // Lead name
  secondaryUserId       // Secondary user ID
);

// Call ended
this.signalRService.sendDialerEvent(
  loginUserId,           // Current logged-in user ID
  leadId,               // Lead ID
  null,                 // Call ended (null)
  leadName,             // Lead name
  secondaryUserId       // Secondary user ID
);
```

### **Backend Receives & Sends (Same AutoDialerLeadDto):**
```csharp
[HubMethodName("SendDialerEventToGroupAsync")]
public async Task SendDialerEventToGroupAsync(string groupName, AutoDialerLeadDto dialerEvent)
{
    // dialerEvent contains:
    // - dialerEvent.Id (leadId)
    // - dialerEvent.AssignTo (loginUserId)
    // - dialerEvent.Name (leadName)
    // - dialerEvent.SecondaryUserId
    // - dialerEvent.CallStartTime (DateTime = call active, null = call ended)

    // Process the event and send back same structure
    var leadDto = new AutoDialerLeadDto
    {
        Id = dialerEvent.Id,
        Name = dialerEvent.Name,
        AssignTo = dialerEvent.AssignTo,
        SecondaryUserId = dialerEvent.SecondaryUserId,
        CallStartTime = dialerEvent.CallStartTime  // Keep same value or update as needed
    };

    await Clients.Group(groupName).SendAsync("ReceiveDialerEvent", leadDto);
}
```

### **Single DTO Structure:**
```csharp
public class AutoDialerLeadDto
{
    public Guid? Id { get; set; }
    public string? Name { get; set; }
    public Guid? AssignTo { get; set; }
    public Guid? SecondaryUserId { get; set; }
    public DateTime? CallStartTime { get; set; }  // null = call ended, DateTime = call active
}
```

### **Key Points:**
- **Single DTO**: Only `AutoDialerLeadDto` used for both directions
- **Simplified**: No separate event types or complex structures
- **CallStartTime**: `DateTime` = call active, `null` = call ended
- **Widget updates**: Based on `CallStartTime` field

## Future Enhancements

- Add more granular call events (ringing, answered, transferred)
- Implement call queue management
- Add real-time analytics and reporting
- Support for multiple concurrent calls per user
