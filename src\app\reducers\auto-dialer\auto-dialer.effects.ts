import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { Store } from "@ngrx/store";
import { NotificationsService } from "angular2-notifications";
import { of } from "rxjs";
import { catchError, map, switchMap } from "rxjs/operators";
import { OnError } from "src/app/app.actions";
import { AppState } from "src/app/app.reducer";
import { AutodialerService } from "src/app/services/controllers/autodialer.service";
import { AutoDialerActionTypes, DialerAddLead, DialerAddLeadSuccess, FetchAutoDialer, FetchAutoDialerSuccess, FetchStatusCount, FetchStatusCountSuccess, GetUserStatus, GetUserStatusSuccess, UpdateUserStatus, UpdateUserStatusSuccess } from "./auto-dialer.actions";

@Injectable()
export class AutoDialerEffects {
    getAutoDialer$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AutoDialerActionTypes.FETCH_AUTO_DIALER),
            map((action: FetchAutoDialer) => action),
            switchMap((action: FetchAutoDialer) => {
                return this.api.getAutoDialer(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchAutoDialerSuccess(resp);
                        }
                        return new FetchAutoDialerSuccess([]);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getStatusCount$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AutoDialerActionTypes.FETCH_STATUS_COUNT),
            map((action: FetchStatusCount) => action),
            switchMap((data: any) => {
                return this.api.getStatusCount().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchStatusCountSuccess(resp);
                        }
                        return new FetchStatusCountSuccess([]);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    updateUserStatus$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AutoDialerActionTypes.UPDATE_USER_STATUS),
            map((action: UpdateUserStatus) => action),
            switchMap((data: any) => {
                return this.api.updateUserStatus().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success('User Status Updated Successfully');
                            this._store.dispatch(new GetUserStatus());
                            return new UpdateUserStatusSuccess();
                        }
                        return new UpdateUserStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getUserStatus$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AutoDialerActionTypes.GET_USER_STATUS),
            map((action: GetUserStatus) => action),
            switchMap((data: any) => {
                return this.api.getUserStatus().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new GetUserStatusSuccess(resp);
                        }
                        return new GetUserStatusSuccess([]);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    addLead$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AutoDialerActionTypes.ADD_LEAD),
            map((action: DialerAddLead) => action.payload),
            switchMap((data: any) => {
                return this.api.addlead(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success('Lead added to Auto Dialer Successfully');
                            return new DialerAddLeadSuccess(resp);
                        }
                        return new DialerAddLeadSuccess([]);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    updateFilterPayload$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AutoDialerActionTypes.UPDATE_FILTER_PAYLOAD),
            switchMap((action: any) => {
                return of(new FetchAutoDialer(action.filter));
            })
        )
    );

    constructor(
        private actions$: Actions,
        private api: AutodialerService,
        private _notificationService: NotificationsService,
        private _store: Store<AppState>
    ) { }
}