import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs/operators';

import { AppState } from 'src/app/app.reducer';
import {
  convertUrlsToLinks,
  getTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import {
  FetchLeadNotesList,
  UpdateLeadNotes
} from 'src/app/reducers/lead/lead.actions';
import {
  getNotesList
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'lead-notes',
  templateUrl: './lead-notes.component.html',
})
export class LeadNotesComponent
  implements OnInit, OnDestroy, AfterViewInit, OnChanges {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() data: any;
  canEditNotes: boolean = false;
  @Input() whatsAppComp: boolean = false;
  @Output() notesAdded: EventEmitter<string> = new EventEmitter<string>();
  notesForm: FormGroup;
  notesHistory: any[] = [];
  options: AnimationOptions = {
    path: 'assets/animations/empty-notes.json',
  };
  isNoteLoading: boolean = true;
  canViewConfidentialNotes: boolean;
  getTimeZoneDate = getTimeZoneDate;
  convertUrlsToLinks = convertUrlsToLinks;
  userData: any;
  @ViewChild('notesInput') notesInput!: ElementRef;

  constructor(
    private fb: FormBuilder,
    private _store: Store<AppState>,
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    private _notificationsService: NotificationsService
  ) {
    this.notesForm = this.fb.group({
      notes: ['', ValidationUtil.cannotBeBlank],
    });
  }

  ngOnInit() {
    if (this.data?.id) {
      this._store.dispatch(new FetchLeadNotesList(this.data.id));
      this._store
        .select(getNotesList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.isNoteLoading = data.LeadNoteIsLoading;
          if (Array.isArray(data.LeadNotes) && data.LeadNotes.length > 0) {
            const sortedLeadNotes = [...data.LeadNotes].sort((a: any, b: any) => {
              return new Date(b.updatedOn).getTime() - new Date(a.updatedOn).getTime();
            });
            const groupedByDate = sortedLeadNotes.reduce((acc: any, curr: any) => {
              const updatedOn = getTimeZoneDate(
                curr.updatedOn,
                this.userData?.timeZoneInfo?.baseUTcOffset,
                'dayMonthYear'
              );
              if (!acc[updatedOn]) {
                acc[updatedOn] = [];
              }
              acc[updatedOn].push(curr);
              return acc;
            }, {});

            this.notesHistory = Object.entries(groupedByDate).map(
              (item: any) => {
                const filteredData = item[1].filter((elem: any) =>
                  this.canViewConfidentialNotes
                    ? (elem.fieldName == 'Notes' || elem.fieldName == 'Confidential Notes')
                    : elem.fieldName == 'Notes'
                );
                const filteredWithValue = filteredData.filter((elem: any) =>
                  elem.newValue && elem.newValue.trim() !== ''
                );
                return {
                  date: item[0],
                  data: filteredWithValue,
                };
              }
            );
          } else {
            this.notesHistory = [];
          }
        });
    }

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Leads.ViewConfidentialNotes'))
          this.canViewConfidentialNotes = true;
        if (permissions?.includes('Permissions.Leads.UpdateNotes'))
          this.canEditNotes = true;
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  ngAfterViewInit(): void {
    if (this.notesInput) {
      setTimeout(() => {
        this.notesInput.nativeElement.focus();
      }, 0);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data?.currentValue?.id !== changes.data?.previousValue?.id &&
      !changes.data?.firstChange
    ) {
      this._store.dispatch(new FetchLeadNotesList(this.data.id));
    }
  }


  get hasNotesHistory(): boolean {
    return this.notesHistory.some((item: any) => item.data.length);
  }

  postNotes() {
    if (!this.data?.name?.trim()) {
      this._notificationsService?.warn(
        'Lead Name is invalid, Please Rename to continue'
      );
      return;
    }
    if (this.notesForm.controls['notes'].valid) {
      const payload = {
        id: this.data?.id,
        notes: this.notesForm.controls['notes'].value,
      };
      this._store.dispatch(new UpdateLeadNotes(this.data?.id, payload));
      this.notesForm.reset();
      this.notesAdded.emit(payload.notes);
    }
  }


  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
