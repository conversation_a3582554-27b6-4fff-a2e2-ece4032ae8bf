import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
@Injectable({
  providedIn: 'root'
})
export class AutodialerService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'autodialer';
  }

  getAutoDialer(payload?: any) {
    if (payload) {
      const params = new URLSearchParams();
      Object.keys(payload).forEach(key => {
        if (payload[key] !== null && payload[key] !== undefined) {
          params.append(key, payload[key].toString());
        }
      });
      return this.http.get(`${this.serviceBaseUrl}?${params.toString()}`);
    }
    return this.http.get(`${this.serviceBaseUrl}`);
  }

  getStatusCount() {
    return this.http.get(`${this.serviceBaseUrl}/statusescount`);
  }

  updateDialerConfiguration(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/config`, payload);
  }

  geteDialerConfiguration() {
    return this.http.get(`${this.serviceBaseUrl}/config`);
  }

  updateUserStatus() {
    return this.http.put(`${this.serviceBaseUrl}/userstatus`, '');
  }

  getUserStatus() {
    return this.http.get(`${this.serviceBaseUrl}/userstatus`);
  }

  addlead(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/addleads`, payload);
  }
}
