import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { skipWhile, takeUntil } from 'rxjs/operators';
import { SHOW_ENTRIES } from 'src/app/app.constants';
import { IVRCallStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getAssignedToDetails, getPages, hexToRgba } from 'src/app/core/utils/common.util';
import { FetchStatusCount, GetUserStatus, UpdateFilterPayload, UpdateUserStatus } from 'src/app/reducers/auto-dialer/auto-dialer.actions';
import { getAutoDialer, getAutoDialerIsLoading, getFiltersPayload, getStatusCount, getStatusCountIsLoading, getTotalCount, getUserStatus } from 'src/app/reducers/auto-dialer/auto-dialer.reducers';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { DialerEventDto, SignalRService } from 'src/app/services/shared/web-socket.service';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';

@Component({
  selector: 'auto-dialer',
  templateUrl: './auto-dialer.component.html',
})
export class AutoDialerComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  filterTabs: any[]
  selectedFilter: string = 'Pending';
  isCountsLoading: boolean = false;
  isAvailable: boolean = false;
  SearchByNameAndNumber: string = '';
  isDataLoading: boolean = false;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  rowData: any[] = [];
  totalCount: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;
  currOffset: number = 0;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: this.pageSize,
    filterType: this.selectedFilter,
  };
  selectedPageSize: number = 10;
  isUsersListForReassignmentLoading: boolean = true;
  gridOptions: GridOptions;
  gridApi: GridApi;

  // Dialer state
  currentLead: any;
  currentCallDuration: string = '00:00';
  isDialing: boolean = false;
  isLeadPreviewOpen: boolean = false;
  callStartTime: Date | null = null;
  callTimer: any;
  getPages = getPages;
  hexToRgba: Function = hexToRgba;
  globalSettingsData: any;
  allUsers: any;

  constructor(
    public modalRef: BsModalRef,
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private modalService: BsModalService,
    private signalRService: SignalRService
  ) {
  }

  ngOnInit(): void {
    this.selectedPageSize = 10;
    this._store.dispatch(new FetchStatusCount());
    this._store.dispatch(new GetUserStatus());
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));

    this._store
      .select(getStatusCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filterTabs = data?.data
      });

    this._store
      .select(getStatusCountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isCountsLoading = data;
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(
        skipWhile((data: any) => !data || data?.length === 0),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        const sortedUsers = data.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
        this.allUsers = sortedUsers.sort(
          (a: any, b: any) =>
            (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
        );
      });

    this._store
      .select(getUserStatus)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.hasOwnProperty('succeeded') && data.hasOwnProperty('data')) {
          this.isAvailable = !!data.data;
        }
      });

    this._store
      .select(getAutoDialer)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data || [];
      });

    this._store
      .select(getTotalCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((count: number) => {
        this.totalCount = count;
      });

    this._store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((payload: any) => {
        this.filtersPayload = payload;
        this.currentPage = payload?.pageNumber || 1;
        this.pageSize = payload?.pageSize || 10;
        this.SearchByNameAndNumber = payload?.SearchByNameAndNumber || '';
        this.selectedFilter = payload?.filterType || 'Pending';
      });

    this._store
      .select(getAutoDialerIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isDataLoading = data;
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data || {}).length) {
          this.globalSettingsData = data;
        }
      });

    // Initialize SignalR for dialer
    this.initializeDialerSignalR();

    this.initializeGridSettings();
    setTimeout(() => {
      this.setCurrentLead(this.rowData[0]);
    }, 2000);
  }

  onFilterChange(filterKey: string): void {
    this.selectedFilter = filterKey;
    const updatedPayload = {
      ...this.filtersPayload,
      filterType: filterKey,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateFilterPayload(updatedPayload));
    this.currOffset = 0;
  }

  onAvailabilityToggle(): void {
    this._store.dispatch(new UpdateUserStatus());
  }

  onSearch($event: any): void {
    if ($event.key === 'Enter') {
      if (this.SearchByNameAndNumber === '' || this.SearchByNameAndNumber === null) {
        return;
      }
      const updatedPayload = {
        ...this.filtersPayload,
        SearchByNameAndNumber: this.SearchByNameAndNumber,
        pageNumber: 1,
      };
      this._store.dispatch(new UpdateFilterPayload(updatedPayload));
      this.currOffset = 0;
    }
  }

  isEmptyInput(): void {
    if (this.SearchByNameAndNumber === '' || this.SearchByNameAndNumber === null) {
      const updatedPayload = {
        ...this.filtersPayload,
        SearchByNameAndNumber: '',
        pageNumber: 1,
      };
      this._store.dispatch(new UpdateFilterPayload(updatedPayload));
      this.currOffset = 0;
    }
  }

  refreshData(): void {
    this._store.dispatch(new FetchStatusCount());
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 50;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Lead Name',
        field: 'name',
        valueGetter: (params: any) => [params.data?.lead.name],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Assigned To',
        field: 'assignedTo',
        minWidth: 180,

        valueGetter: (params: any) => [
          getAssignedToDetails(params.data?.lead?.assignTo, this.allUsers, true) ||
          '--',
          getAssignedToDetails(
            params.data.lead.secondaryUserId,
            this.allUsers,
            true
          ) || '--',
        ],
        colId: 'AssignTo',
        cellRenderer: (params: any) => {
          if (this.globalSettingsData?.isDualOwnershipEnabled) {
            return `<div class="d-flex">
                     <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">P</div>
                     <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : '--'
              }</p>
                   </div>
                   <div class="d-flex mt-6">
                     <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">S</div>
                     <p class="text-truncate-1 break-all">${params.value[1] ? params.value[1] : '--'
              }</p>
                   </div>`;
          } else {
            return `<div class="d-flex">
                     <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
              }</p>
                   </div>`;
          }
        },
      },
      {
        headerName: 'Status',
        field: 'status',
        valueGetter: (params: any) => {
          return [
            params?.data?.lead?.status?.displayName,
            params?.data?.lead?.status?.actionName,
            params?.data?.lead?.status?.childType?.displayName,
            params?.data?.lead?.status?.childType?.actionName,
          ];
        },
        cellRenderer: (params: any) => {
          const status = params.data.lead.status?.displayName || '--';
          const childStatus = params.data.lead.status?.childType?.displayName || '--';
          let isNegStatus: boolean;
          isNegStatus = [
            'Overdue',
            'Not Interested',
            'Dropped',
            'Booking Cancel',
            'Pending',
          ].includes(status);
          const statusClass = this.globalSettingsData?.isCustomStatusEnabled
            ? 'text-black'
            : isNegStatus
              ? 'text-danger'
              : 'text-accent-green';

          return `
      <p class="text-truncate fw-600 ${statusClass}">${status === 'Site Visit Scheduled' && this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : status}</p>
      <p class="text-truncate mt-4">${childStatus}</p>
    `;
        },
        minWidth: 120,
      },
      {
        headerName: 'Call Status',
        field: 'callStatus',
        valueGetter: (params: any) => params.data?.callStatus !== 0 ? params.data?.callStatus : '--',
        cellRenderer: (params: any) => {
          if (params.value === '--') {
            return `<span>--</span>`;
          }
          const color = this.getStatusColor(params.value);
          return `
            <span class="status-label-badge" style="background-color: ${this.hexToRgba(
            color,
            0.08
          )};">
              <p class="mr-6" style="color: ${color};">${this.getStatusLabel(params.value)}</p>
            </span>`;
        },
        cellClass: 'cursor-pointer',
        minWidth: 140,
      },
      {
        headerName: 'Source',
        field: 'source',
        valueGetter: (params: any) => [params.data?.lead?.source || '--'],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
        minWidth: 150,
      },
    ];
    this.gridOptions.context = {
      componentParent: this,
    };
    this.gridOptions.onCellClicked = this.onCellClicked.bind(this);
  }

  getStatusLabel(status: IVRCallStatus): string {
    return IVRCallStatus[status] || '';
  }

  getStatusColor(status: IVRCallStatus): string {
    const statusColorMap: Record<any, string> = {
      [IVRCallStatus.None]: '',
      [IVRCallStatus.Pending]: '#6850BF',
      [IVRCallStatus.Initiated]: '#3A6DAF',
      [IVRCallStatus.Ringing]: '#FF0000',
      [IVRCallStatus.Answered]: '#ED5454',
      [IVRCallStatus.Connected]: '#50BFA8',
    };
    return statusColorMap[status] || '#000000';
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
  }

  onCellClicked(event: any): void {
    if (event.data && event.colDef.field) {
      const selectedSection = event.colDef.field === 'status' ? 'Status' : 'Overview';
      this.openLeadPreview(event.data, selectedSection);
    }
  }

  openLeadPreview(leadData: any, selectedSection: string = 'Overview'): void {
    this.isLeadPreviewOpen = true;
    const initialState = {
      data: leadData?.lead,
      selectedSection: selectedSection,
      cardData: this.rowData?.map((item: any) => item.lead),
      isFromAutoDialer: true,
      currentDialerLead: this.currentLead?.lead,
      closeLeadPreviewModal: () => {
        this.isLeadPreviewOpen = false;
        modalRef.hide();
      }
    };
    const modalRef = this.modalService.show(LeadPreviewComponent, {
      class: 'right-modal modal-550 ip-modal-unset',
      initialState: initialState
    });
    modalRef.onHide?.subscribe(() => {
      this.isLeadPreviewOpen = false;
    });
  }

  onPageChange(event: any): void {
    this.currOffset = event;
    const updatedPayload = {
      ...this.filtersPayload,
      pageNumber: event + 1,
      pageSize: this.pageSize,
    };
    this._store.dispatch(new UpdateFilterPayload(updatedPayload));
  }

  onPageSizeChange(event: any): void {
    const updatedPayload = {
      ...this.filtersPayload,
      pageSize: event,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateFilterPayload(updatedPayload));
    this.currOffset = 0;
  }

  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'new':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'call back':
        return 'badge-info';
      case 'converted':
        return 'badge-primary';
      default:
        return 'badge-secondary';
    }
  }

  getCallStatusClass(callStatus: string): string {
    switch (callStatus?.toLowerCase()) {
      case 'completed':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'failed':
        return 'badge-danger';
      case 'skipped':
        return 'badge-secondary';
      case 'retry scheduled':
        return 'badge-info';
      default:
        return 'badge-light';
    }
  }

  getSourceIcon(source: string): string {
    switch (source?.toLowerCase()) {
      case 'linkedin':
        return 'ic-linkedin icon ic-xs text-primary';
      case 'magicbricks':
        return 'ic-magicbricks icon ic-xs text-danger';
      case 'direct/walk-in':
        return 'ic-user icon ic-xs text-secondary';
      default:
        return 'ic-globe icon ic-xs text-muted';
    }
  }

  callLead(): void {
    if (this.currentLead) {
      this.isDialing = true;
      this.startCallTimer();

      // Notify via SignalR that call has been initiated
      this.signalRService.sendDialerEvent(
        this.currentLead.id,
        IVRCallStatus.Initiated,
        0,
        'CallStatusUpdate'
      );
    }
  }

  skipLead(): void {
    if (this.currentLead) {
      // Notify via SignalR that lead was skipped
      this.signalRService.sendDialerEvent(
        this.currentLead.id,
        IVRCallStatus.Aborted,
        0,
        'CallStatusUpdate'
      );
    }

    this.stopCallTimer();
    this.currentLead = null;
    this.isDialing = false;
  }

  endCall(): void {
    if (this.currentLead) {
      // Calculate call duration
      const callDuration = this.getCallDurationInSeconds();

      // Notify via SignalR that call has ended
      this.signalRService.sendDialerEvent(
        this.currentLead.id,
        IVRCallStatus.Completed,
        callDuration,
        'CallStatusUpdate'
      );
    }

    this.stopCallTimer();
    this.currentLead = null;
    this.isDialing = false;
    this.currentCallDuration = '00:00';
  }

  private startCallTimer(): void {
    this.callStartTime = new Date();
    this.currentCallDuration = '00:00';

    // Start real-time timer
    this.callTimer = setInterval(() => {
      if (this.callStartTime) {
        const elapsed = Math.floor((new Date().getTime() - this.callStartTime.getTime()) / 1000);
        this.currentCallDuration = this.formatCallDuration(elapsed);
      }
    }, 1000);
  }

  private stopCallTimer(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
    this.callStartTime = null;
  }

  private getCallDurationInSeconds(): number {
    if (this.callStartTime) {
      return Math.floor((new Date().getTime() - this.callStartTime.getTime()) / 1000);
    }
    return 0;
  }

  setCurrentLead(lead: any): void {
    this.currentLead = lead?.lead;
  }

  private initializeDialerSignalR(): void {
    this.signalRService.startDialerConnection();
    this.signalRService.getDialerEventListener()
      .pipe(takeUntil(this.stopper))
      .subscribe((event: DialerEventDto) => {
        console.log('Received dialer event:', event);
        this.handleDialerEvent(event);
      });
  }

  // Handle incoming dialer events
  private handleDialerEvent(event: DialerEventDto): void {
    console.log('Received dialer event:', event);

    // Only handle events for the current screen/user
    if (event.eventType === 'CallStatusUpdate') {
      this.handleCallStatusUpdate(event);
    }
  }

  // Handle call status updates for the current screen widget
  private handleCallStatusUpdate(event: DialerEventDto): void {
    // Only update if this is the current lead being displayed in the widget
    if (this.currentLead && event.leadId === this.currentLead.id) {
      // Update call status
      this.currentLead.callStatus = event.callStatus;

      // Update call duration if provided
      if (event.callDuration !== undefined) {
        this.currentCallDuration = this.formatCallDuration(event.callDuration);
      }

      // Update dialing state based on call status
      switch (event.callStatus) {
        case IVRCallStatus.Initiated:
        case IVRCallStatus.Ringing:
        case IVRCallStatus.Connected:
        case IVRCallStatus.InProgress:
          this.isDialing = true;
          break;
        case IVRCallStatus.Completed:
        case IVRCallStatus.Failed:
        case IVRCallStatus.Aborted:
        case IVRCallStatus.Dropped:
          this.isDialing = false;
          this.stopCallTimer();
          break;
      }

      console.log(`Call status updated for lead ${event.leadId}: ${IVRCallStatus[event.callStatus || 0]}`);
    }
  }

  // Format call duration for display
  private formatCallDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  ngOnDestroy(): void {
    // Stop call timer if running
    this.stopCallTimer();

    // Stop dialer SignalR connection
    this.signalRService.stopDialerConnection();

    this.stopper.emit();
    this.stopper.complete();
  }

  closeModal(): void {
    this.modalRef.hide();
  }
}
