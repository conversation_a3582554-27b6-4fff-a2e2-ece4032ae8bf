<div class="justify-center">
  <div class="position-absolute w-80pr bg-slate-140 bottom-12 br-12 box-shadow-10 px-16 py-12 z-index-2 tb-flex-col">
    <div class="flex-between">
      <div class="flex-col">
        <span class="fw-600 text-white text-xl mr-4">{{gridApi?.getSelectedNodes()?.length}}</span>
        <span class="text-white fw-400 mr-20">{{gridApi?.getSelectedNodes()?.length > 1 ? 'Leads' : 'Lead'}} {{
          'LEADS.selected'
          |
          translate}}</span>
      </div>
      <div class="d-flex scrollbar max-w-100-260 tb-max-w-100-190 ip-max-w-100-70 scroll-hide"
        (scroll)="onScrollableContainerScroll()">
        <div class="flex-center">
          <div class="flex-center">
            <ng-container *ngFor="let button of visibleButtons | slice:0:4">
              <ng-container *ngTemplateOutlet="buttonTemplate; context: { $implicit: button }"></ng-container>
            </ng-container>
            <!-- More Actions dropdown -->
            <div #moreActionsDropdownRef="bs-dropdown" dropdown container="body" class="position-relative"
              *ngIf="visibleButtons?.length > 4">
              <button class="btn-bulk-black align-center" dropdownToggle id="moreActionsDropdown"
                data-automate-id="moreActionsDropdown">
                <span class="ic-swipe-up icon ic-xs mr-8"></span>
                More Actions
                <span class="ic-triangle rotate-180 icon ic-xx-xs ml-8"></span>
              </button>
              <div class="position-absolute bg-black-80 bottom-45 nleft-55 br-6 py-8 px-20" *dropdownMenu>
                <ng-container *ngFor="let button of visibleButtons | slice:4">
                  <ng-container *ngTemplateOutlet="buttonTemplate; context: { $implicit: button }"></ng-container>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Button Template -->
<ng-template #buttonTemplate let-button>
  <button *ngIf="button.id === 'bulkUpdateStatus'" class="btn-bulk-black w-100 align-center"
    (click)="openBulkUpdateStatusModal(BulkUpdateStatusModal); $event?.stopPropagation()">
    <span class="ic-user-arrow icon ic-xs mr-8"></span>
    Bulk {{ 'GLOBAL.update' | translate }} {{ 'GLOBAL.status' | translate }}
  </button>
  <button *ngIf="button.id === 'bulkReassign'" class="btn-bulk-black w-100 align-center" id="btnBulkReassign"
    data-automate-id="btnBulkReassign" (click)="openBulkReassignModal(BulkReassignModal)">
    <span class="ic-round-circles icon ic-xs mr-8"></span>
    {{ (leadVisibilityEnum[appliedFilter?.leadVisibility] === 'Unassigned' ? 'LEADS.assign' : 'GLOBAL.reassign')
    |
    translate }}
    {{ 'GLOBAL.leads' | translate }}
  </button>
  <button *ngIf="button.id === 'bulkSecondaryReassign'" class="btn-bulk-black w-100 align-center"
    id="btnBulkSecondaryReassign" data-automate-id="btnBulkSecondaryReassign"
    (click)="openBulkSecondaryReassignModal(BulkSecondaryReassignModal)">
    <span class="ic-refine icon ic-xs mr-8"></span>
    {{ (leadVisibilityEnum[appliedFilter?.leadVisibility] === 'Unassigned' ? 'Secondary Assign' : 'Secondary
    Reassign') | translate }}
    {{ 'GLOBAL.leads' | translate }}
  </button>
  <button *ngIf="button.id === 'bulkSource'" class="btn-bulk-black w-100 align-center" id="btnBulkSource"
    data-automate-id="btnBulkSource" (click)="openBulkSourceModal(BulkSourceModal)">
    <span class="ic-circle-nodes icon ic-xs mr-8"></span>
    Bulk {{ 'LEADS.source' | translate }}
  </button>
  <button *ngIf="button.id === 'bulkProject'" class="btn-bulk-black w-100 align-center" id="btnBulkProject"
    data-automate-id="btnBulkProject" (click)="openBulkProjectModal(BulkProjectModal)">
    <span class="ic-buliding-secondary-solid icon ic-xs mr-8"></span>
    Bulk {{ 'SIDEBAR.project' | translate }}
  </button>
  <button *ngIf="button.id === 'bulkAgency'" class="btn-bulk-black w-100 align-center" id="btnBulkAgency"
    data-automate-id="btnBulkAgency" (click)="openBulkAgencyModal(BulkAgencyModal)">
    <span class="ic-briefcase-solid icon ic-xs mr-8"></span>
    Bulk Agency
  </button>
  <button *ngIf="button.id === 'bulkChannelPartner'" class="btn-bulk-black w-100 align-center"
    id="btnBulkChannelPartner" data-automate-id="btnBulkChannelPartner"
    (click)="openBulkChannelPartnerModal(BulkChannelPartnerModal)">
    <span class="ic-handshake-solid icon ic-xs mr-8"></span>
    Bulk Channel Partner
  </button>
  <button *ngIf="button.id === 'autoDialer'" class="btn-bulk-black align-center" id="btnBulkAutoDialer"
    data-automate-id="btnBulkAutoDialer" (click)="openAutoDialer()"><span
      class="ic-call-forwarding icon ic-xs mr-8"></span>
    Add to Autodailer</button>
  <button *ngIf="button.id === 'bulkCampaign'" class="btn-bulk-black w-100 align-center" id="btnBulkCampaign"
    data-automate-id="btnBulkCampaign" (click)="openBulkCampaignModal(BulkCampaignModal)">
    <span class="ic-address-card-solid icon ic-xs mr-8"></span>
    Bulk Campaign
  </button>
  <button *ngIf="button.id === 'bulkWhatsapp'" class="btn-bulk-black w-100 align-center" (click)="openBulkShareModal()">
    <span class="ic-whatsapp icon ic-xs mr-8"></span>
    Bulk WhatsApp
  </button>
  <button *ngIf="button.id === 'bulkEmail'" class="btn-bulk-black w-100 align-center"
    (click)="openEmailConfirmation(changePopup, noMail)">
    <span class="ic-envelope-solid icon ic-xs mr-8"></span>
    Bulk Email
  </button>
  <button *ngIf="button.id === 'bulkRestore'" class="btn-bulk-black w-100 align-center"
    (click)="bulkPermanentDelete = true; openBulkDeleteModal(BulkDeleteModal)">
    {{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.restore' | translate }}
  </button>
  <button *ngIf="button.id === 'bulkDelete'" class="btn-bulk-black w-100 align-center"
    (click)="bulkPermanentDelete = false; openBulkDeleteModal(BulkDeleteModal)">
    <span class="ic-trash icon ic-xs mr-8"></span>
    {{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.delete' | translate }}
  </button>
</ng-template>
<ng-template #changePopup>
  <div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
    <!-- <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div> -->
    <div class="flex-end mt-30">
      <button class="btn-gray mr-20" (click)="modalRef.hide()" id="clkSettingsNo" data-automate-id="clkSettingsNo">
        {{ 'GLOBAL.no' | translate }}</button>
      <button class="btn-green" (click)="openBulkEmailPopup()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
        {{ 'GLOBAL.yes' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #noMail>
  <div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
    <!-- <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div> -->
    <div class="flex-end mt-30">
      <button class="btn-green" (click)="modalRef.hide()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
        Close</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkUpdateStatusModal>
  <bulk-update-status-leads [modal]="bulkUpdateModalRef" [leadInput]="sameStatusRows"
    (modelOutput)="emitModalOutput($event)" [selectedNodes]="selectedNodes" [gridApi]="gridApi">
  </bulk-update-status-leads>
</ng-template>

<ng-template #BulkReassignModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern" [ngClass]="{'pe-none': bulkReassignLeadsIsLoading}">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{ 'GLOBAL.reassign' | translate }} {{ 'GLOBAL.leads' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 scrollbar h-100-108">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedBulkReassign?.length}}</div>
      <div class="scrollbar table-scrollbar">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">
                <div class="justify-between">
                  <span>{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</span>
                  <span class="icon ic-xx-xs my-auto cursor-pointer"
                    [ngClass]="!isLeadNameColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                    (click)="sortColumn('name', isLeadNameColInAscOrder)"></span>
                </div>
              </th>
              <th class="w-120">
                <div class="justify-between">
                  <span>{{ 'LEADS.assign-to' | translate }}</span>
                  <span class="icon ic-xx-xs my-auto cursor-pointer"
                    [ngClass]="!isAssignToColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                    (click)="sortColumn('assignTo', isAssignToColInAscOrder)"></span>
                </div>
              </th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold max-h-100-240">
            <ng-container>
              <tr *ngFor="let lead of selectedBulkReassign">
                <td class="w-120" [title]="lead.name">
                  <div class="text-truncate-1 break-all"> {{ lead.name }} </div>
                </td>
                <td class="w-120" [title]="assignToName(lead)">
                  <div class="text-truncate-1 break-all">
                    {{assignToName(lead)}}
                  </div>
                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead.name, lead.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkRessiagn" data-automate-id="clkDeleteBulkRessiagn">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkReassignForm">
        <div class="d-flex flex-wrap">
          <div *ngFor="let assignmentTypeOption of assignmentTypeOptions" class="form-check form-check-inline p-0 mr-0"
            [ngClass]="{'pe-none': isUnassignLeadSelected}">
            <div class="align-center mt-20 mr-20">
              <input type="radio" [id]="assignmentTypeOption?.value + 'assignmentTypeOptionRadio'"
                class="radio-check-input border-remove" [value]="assignmentTypeOption.value"
                formControlName="assignmentType" name="assignmentType" [disabled]="isUnassignLeadSelected"
                (change)="onAssignmentTypeChange(assignmentTypeOption.label)">
              <label class="fw-600 text-dark-800 cursor-pointer text-sm ml-8"
                [for]="assignmentTypeOption?.value + 'assignmentTypeOptionRadio'">
                {{ assignmentTypeOption.label }}
              </label>
            </div>
          </div>
        </div>
        <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
        <div class="text-sm text-black-100 mb-4 pt-2" *ngIf="isDualOwnershipEnabled">Primary</div>
        <form-errors-wrapper [control]="bulkReassignForm.controls['assignedToUsers']"
          label="{{'LEADS.assign-to' | translate}}" class="ng-select-sm-gray">
          <ng-select [virtualScroll]="true" placeholder="Select User" name="user" class="bg-white" ResizableDropdown
            [items]="assignToUsersList" [multiple]="true" [searchable]="true" [closeOnSelect]="false"
            formControlName="assignedToUsers"
            *ngIf="!((canAssignToAny && usersListForReassignmentIsLoading) || (!canAssignToAny && adminsAndReporteesIsLoading)) else fieldLoader"
            (change)="assignToUserListChanged()">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                  *ngIf="!isUnassignLeadSelected || item.value===''" class="checkmark"></span>{{item.label}}</div>
            </ng-template>
          </ng-select>
        </form-errors-wrapper>
        <h5 class="text-dark-800 fw-600 mt-16">{{'GLOBAL.select-your-preferences' | translate}}</h5>
        <div class="d-flex">
          <label class="checkbox-container mt-12" [ngClass]="{'pe-none': isUnassignLeadSelected}"
            *ngIf="canUpdateSource && canViewLeadSource && globalSettingsData?.isLeadSourceEditable">
            <input type="checkbox" [checked]="bulkReassignForm.get('isChangeSourceSelected').value" (click)="trackingService.trackFeature('Web.Leads.Options.changeSource.Click')
            " [disabled]="isUnassignLeadSelected" formControlName="isChangeSourceSelected">
            <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.change-source'
              | translate}}</span>
          </label>
        </div>
        <div class="d-flex w-100 ng-select-sm"
          *ngIf="bulkReassignForm.get('isChangeSourceSelected').value && canUpdateSource && canViewLeadSource && globalSettingsData?.isLeadSourceEditable">
          <div class="w-50">
            <div class="field-label-req text-sm">{{'LEADS.source' | translate}}</div>
            <form-errors-wrapper [control]="bulkReassignForm.controls['selectedSource']"
              label="{{'LEADS.source' | translate}}">
              <ng-select [virtualScroll]="true" placeholder="Select Source" name="source" ResizableDropdown
                formControlName="selectedSource" class="mr-10 bg-white" (change)="onSourceChange()">
                <ng-option *ngFor="let source of leadSources" [value]="source" [disabled]="!source.isEnabled"
                  [ngClass]="{'pe-none': !source.isEnabled}">
                  <div class="dropdown-position">
                    <span class="text-truncate-1 break-all">{{ source.displayName }}</span>
                    <span class="text-disabled" *ngIf="!source.isEnabled"> (Disabled)</span>
                  </div>
                </ng-option>
              </ng-select>
            </form-errors-wrapper>
          </div>
          <div class="w-50 ml-10">
            <div class="field-label text-sm">{{'LEADS.sub-source' | translate}}</div>
            <ng-select [virtualScroll]="true" *ngIf="!subSourceListIsLoading else fieldLoader" ResizableDropdown
              placeholder="Select Sub-Source" name="source" formControlName="selectedSubSource" class="bg-white"
              [addTag]="true">
              <ng-option *ngFor="let subSource of subSourceList" [value]="subSource">
                {{ subSource }}
              </ng-option>
            </ng-select>
          </div>
        </div>
        <div class="d-flex">
          <label class="checkbox-container mt-16" [ngClass]="{'pe-none': isUnassignLeadSelected}">
            <input type="checkbox" [checked]="bulkReassignForm.get('isChangeProjectSelected').value"
              (click)="trackingService.trackFeature('Web.Leads.Options.ChangeProject.Click')"
              [disabled]="isUnassignLeadSelected" formControlName="isChangeProjectSelected">
            <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.change-project'
              | translate}}</span>
          </label>
        </div>
        <div *ngIf="bulkReassignForm.get('isChangeProjectSelected').value" class="w-50">
          <div class="field-label-req text-sm">{{'GLOBAL.project-name' | translate}}</div>
          <form-errors-wrapper [control]="bulkReassignForm.controls['selectedProject']"
            label="{{'GLOBAL.project-name' | translate}}" class="ng-select-sm-gray">
            <ng-select [virtualScroll]="true" placeholder="Select Project" name="project" class="bg-white"
              ResizableDropdown [items]="projectList" *ngIf="!projectListIsLoading else fieldLoader" [multiple]="true"
              [searchable]="true" [closeOnSelect]="false" formControlName="selectedProject">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="project-{{index}}"
                    data-automate-id="project-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item}}</span></div>
              </ng-template>
            </ng-select>
          </form-errors-wrapper>
        </div>
        <div class="d-flex">
          <label *ngIf="isDuplicateFeature" class="checkbox-container mt-16"
            [ngClass]="{'pe-none': isUnassignLeadSelected, 'blinking pe-none': !isDuplicateFeatureSettingsFetched}">
            <input type="checkbox" [checked]="bulkReassignForm.get('isCreateDuplicateSelected').value"
              (click)="trackingService.trackFeature('Web.Leads.Button.CreateDuplicate.Click')"
              [disabled]="isUnassignLeadSelected" formControlName="isCreateDuplicateSelected"
              [attr.disabled]="isDuplicateFeatureSettingsFetched && !isDuplicateFeatureAdded ? true : null">
            <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.create-duplicate'
              | translate}}</span> <span class="fw-semi-bold text-xs text-dark-800 ml-10"
              *ngIf="isDuplicateFeatureSettingsFetched && !isDuplicateFeatureAdded">Disabled</span>
            <div class="fw-semi-bold text-xs text-mud">This will create a duplicate of the current lead(s)</div>
          </label>
        </div>
      </form>
    </div>
    <div class="flex-center mt-20" *ngIf="!bulkReassignLeadsIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkReassign" data-automate-id="btnSaveBulkReassign"
        (click)="updateBulkAssign('bulkReassign')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkSecondaryReassignModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern" [ngClass]="{'pe-none': bulkReassignLeadsIsLoading}">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} Secondary {{ 'GLOBAL.reassign' | translate }} {{ 'GLOBAL.leads' | translate
        }}
      </h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 scrollbar h-100-108 ph-h-100-170">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedBulkReassign?.length}}</div>
      <div class="scrollbar table-scrollbar">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">
                <div class="flex-between">
                  <span>{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</span>
                  <span class="icon ic-xx-xs my-auto cursor-pointer"
                    [ngClass]="!isLeadNameColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                    (click)="sortColumn('name', isLeadNameColInAscOrder)"></span>
                </div>
              </th>
              <th class="w-120">
                <div class="flex-between">
                  <span>{{ 'LEADS.assign-to' | translate }}</span>
                  <span class="icon ic-xx-xs my-auto cursor-pointer"
                    [ngClass]="!isAssignToColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                    (click)="sortColumn('secondaryUserId', isAssignToColInAscOrder)"></span>
                </div>
              </th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold scrollbar max-h-100-265">
            <ng-container>
              <tr *ngFor="let lead of selectedBulkReassign">
                <td class="w-120" [title]="lead.name">
                  <div class="text-truncate-1 break-all"> {{ lead.name }} </div>
                </td>
                <td class="w-120" [title]="secondaryAssignToName(lead)">
                  <div class="align-center" *ngIf="lead?.secondaryUserId && lead?.secondaryUserId !== EMPTY_GUID">
                    <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">S</div>
                    <p class="text-truncate-1 break-all">{{secondaryAssignToName(lead)}}</p>
                  </div>
                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead.name, lead.id)" class="bg-light-red icon-badge"
                    id="clkDeleteSecondaryBulkRessiagn" data-automate-id="clkDeleteSecondaryBulkRessiagn">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
      <div class="ng-select-sm-gray">
        <!-- <form-errors-wrapper [control]="bulkReassignForm.controls['assignedToUsers']"
          label="{{'LEADS.assign-to' | translate}}"> -->
        <form [formGroup]="bulkSecondaryReassignForm">
          <div class="text-sm text-black-100 mb-4 pt-2">secondary</div>
          <form-errors-wrapper [control]="bulkSecondaryReassignForm.controls['secondary']" label="secondary">
            <ng-select placeholder="Select User" name="user" class="bg-white" [items]="assignToUsersList"
              ResizableDropdown formControlName="secondary" [multiple]="true" [searchable]="true"
              [closeOnSelect]="false"
              *ngIf="!((canAssignToAny && usersListForReassignmentIsLoading) || (!canAssignToAny && adminsAndReporteesIsLoading)) else fieldLoader">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    *ngIf="!isUnassignLeadSelected || item.value===''" class="checkmark"></span>{{item.label}}</div>
              </ng-template>
            </ng-select>
          </form-errors-wrapper>
        </form>
        <!-- </form-errors-wrapper> -->
      </div>
    </div>
    <div class="flex-center mt-20" *ngIf="!bulk2ndReassignIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveSecondaryBulkReassign" data-automate-id="btnSaveSecondaryBulkReassign"
        (click)="updateSecondaryBulkAssign('bulkReassign')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkSourceModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern" [ngClass]="{'pe-none': bulkSourceIsLoading}">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{ 'LEADS.source' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 h-100-114">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedSources?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-60">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120"> {{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</th>
              <th class="w-120">{{ 'LEADS.source' | translate }}</th>
              <th class="w-120 text-nowrap">{{ 'LEADS.sub-source' | translate }}</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold  max-h-100-332 scrollbar">
            <ng-container>
              <tr *ngFor="let lead of selectedSources">
                <td class="w-120">
                  <div class="text-truncate-1 break-all"> {{ lead.name }} </div>
                </td>
                <td class="w-120">
                  <div class="text-truncate-1 break-all">{{ LeadSource[lead.enquiry?.leadSource] }}</div>
                </td>
                <td class="w-120">
                  <div class="text-truncate-1 break-all">{{ lead.enquiry?.subSource }}</div>
                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkSource" data-automate-id="clkDeleteBulkSource">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkSourceForm" class="ng-select-sm">
        <div class="field-label-req">{{ 'LEADS.source' | translate }}</div>
        <form-errors-wrapper [control]="bulkSourceForm.controls['source']" label="source">
          <ng-select [virtualScroll]="true" placeholder="Select Source" name="source" ResizableDropdown
            formControlName="source"
            (change)="updateSubSourceForBulkSource(); trackingService.trackFeature('Web.Leads.Options.Source.Click')">
            <ng-option *ngFor="let source of leadSources" [value]="source" [disabled]="!source.isEnabled">
              <div class="dropdown-position">
                <span class="text-truncate-1 break-all">{{ source.displayName }}</span>
              </div>
            </ng-option>
          </ng-select>
        </form-errors-wrapper>
        <ng-container *ngIf="bulkSourceForm.get('source').value">
          <div class="field-label">{{'LEADS.sub-source' | translate}}</div>
          <ng-select [virtualScroll]="true" [items]="subSourceList" [addTag]="true" addTagText="Create New sub-source"
            ResizableDropdown formControlName="subsource"
            placeholder="{{ 'GLOBAL.select' | translate }}/Create {{ 'LEADS.sub-source' | translate }}">
          </ng-select>
        </ng-container>
      </form>
    </div>
    <div class="flex-center mt-20 mb-40" *ngIf="!bulkSourceIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkSource" data-automate-id="btnSaveBulkSource"
        (click)="updateBulkSource('bulkSource')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkProjectModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{ 'PROJECTS.projects' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 h-100-114">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedProjects?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-60">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</th>
              <th class="w-120">{{ 'PROJECTS.projects' | translate }}</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
            <ng-container>
              <tr *ngFor="let lead of selectedProjects">
                <td class="w-120"><span class="text-truncate-1 break-all">{{ lead.name }}</span></td>
                <td class="w-120">
                  <div class="d-flex text-truncate-1 break-all">
                    <span *ngFor="let project of lead.projects; let i = index">
                      <span class="text-nowrap" [title]="getProjectNames(lead)">{{ project.name }}</span>
                      <span *ngIf="i < lead.projects.length - 1">, </span>
                    </span>
                  </div>

                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkProject" data-automate-id="clkDeleteBulkProject">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkProjectFrom">
        <div class="mt-20">
          <div class="justify-between">
            <div class="field-label mt-0">{{'PROJECTS.projects'| translate}}</div>
            <label class="checkbox-container mb-4">
              <input type="checkbox" formControlName="ShouldRemoveExistingProjects"
                (click)="trackingService.trackFeature('Web.Leads.Button.RemoveExistingProjects.Click')">
              <span class="checkmark"></span>Remove Existing Project(s)
            </label>
          </div>
        </div>
        <form-errors-wrapper [control]="bulkProjectFrom.controls['project']" label="project">
          <ng-select [virtualScroll]="true" [items]="projectList" *ngIf="!projectListIsLoading else fieldLoader"
            ResizableDropdown [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
            (change)="trackingService.trackFeature('Web.Leads.Options.projects.Click')" formControlName="project"
            class="bg-white">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </form-errors-wrapper>
      </form>
    </div>
    <div class="flex-center mt-20 mb-40" *ngIf="!bulkProjectsIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkProject" data-automate-id="btnSaveBulkProject"
        (click)="updateBulkProjects('bulkProject')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>
<ng-template #BulkAgencyModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{'REPORTS.agency' | translate}}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 h-100-114">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedAgencies?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-60">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</th>
              <th class="w-120">{{'REPORTS.agency' | translate}}(s)</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
            <ng-container>
              <tr *ngFor="let lead of selectedAgencies">
                <td class="w-120"><span class="text-truncate-1 break-all">{{ lead.name }}</span></td>
                <td class="w-120">
                  <div class="d-flex text-truncate-1 break-all">
                    <span *ngFor="let agency of lead.agencies; let i = index">
                      <span class="text-nowrap" [title]="getAgencyNames(lead)">{{ agency.name }}</span>
                      <span *ngIf="i < lead.agencies.length - 1">, </span>
                    </span>
                  </div>

                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkProject" data-automate-id="clkDeleteBulkProject">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkAgencyFrom">
        <div class="mt-20">
          <div class="justify-between">
            <div class="field-label mt-0">{{'REPORTS.agency' | translate}}(s)</div>
            <label class="checkbox-container mb-4">
              <input type="checkbox" formControlName="shouldRemoveExistingAgency">
              <span class="checkmark"></span>Remove Existing Agency(s)
            </label>
          </div>
        </div>
        <form-errors-wrapper [control]="bulkAgencyFrom.controls['agency']" label="agency">
          <ng-select [virtualScroll]="true" [items]="agencyList" *ngIf="!agencyListIsLoading else fieldLoader"
            ResizableDropdown [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
            (change)="trackingService.trackFeature('Web.Leads.Options.agency.Click')" formControlName="agency"
            class="bg-white">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </form-errors-wrapper>
      </form>
    </div>
    <div class="flex-center mt-20 mb-40" *ngIf="!bulkAgencyIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkAgency" data-automate-id="btnSaveBulkAgency"
        (click)="updateBulkAgency('bulkAgency')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>
<ng-template #BulkChannelPartnerModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{'INTEGRATION.channel-partner' | translate}}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 h-100-114">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedChannelPartners?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-60">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</th>
              <th class="w-120">{{'INTEGRATION.channel-partner' | translate}}(s)</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
            <ng-container>
              <tr *ngFor="let lead of selectedChannelPartners">
                <td class="w-120"><span class="text-truncate-1 break-all">{{ lead.name }}</span></td>
                <td class="w-120">
                  <div class="d-flex text-truncate-1 break-all">
                    <span *ngFor="let channelPartner of lead.channelPartners; let i = index">
                      <span class="text-nowrap" [title]="getChannelPartnerNames(lead)">{{ channelPartner.name
                        }}</span>
                      <span *ngIf="i < lead.channelPartners.length - 1">, </span>
                    </span>
                  </div>

                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkProject" data-automate-id="clkDeleteBulkProject">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkChannelPartnerForm">
        <div class="mt-20">
          <div class="justify-between">
            <div class="field-label mt-0">{{'INTEGRATION.channel-partner' | translate}}(s)</div>
            <label class="checkbox-container mb-4">
              <input type="checkbox" formControlName="shouldRemoveExistingChannelPartener">
              <span class="checkmark"></span>Remove Existing Channel Partner(s)
            </label>
          </div>
        </div>
        <form-errors-wrapper [control]="bulkChannelPartnerForm.controls['channelPartner']" label="channelPartner">
          <ng-select [virtualScroll]="true" [items]="channelPartnerList"
            *ngIf="!channelPartnerListIsLoading else fieldLoader" ResizableDropdown [multiple]="true"
            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
            (change)="trackingService.trackFeature('Web.Leads.Options.channelPartner.Click')"
            formControlName="channelPartner" class="bg-white">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </form-errors-wrapper>
      </form>
    </div>
    <div class="flex-center mt-20 mb-40" *ngIf="!bulkCPIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkChannelPartner" data-automate-id="btnSaveBulkChannelPartner"
        (click)="updateBulkChannelPartner('bulkChannelPartner')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>
<ng-template #BulkCampaignModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{'INTEGRATION.campaign' | translate}}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 h-100-114">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedCampaigns?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-60">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</th>
              <th class="w-120">{{'INTEGRATION.campaign' | translate}}(s)</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
            <ng-container>
              <tr *ngFor="let lead of selectedCampaigns">
                <td class="w-120"><span class="text-truncate-1 break-all">{{ lead.name }}</span></td>
                <td class="w-120">
                  <div class="d-flex text-truncate-1 break-all">
                    <span *ngFor="let campaign of lead.campaigns; let i = index">
                      <span class="text-nowrap" [title]="getCampaignNames(lead)">{{ campaign.name }}</span>
                      <span *ngIf="i < lead.agencies.length - 1">, </span>
                    </span>
                  </div>

                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkProject" data-automate-id="clkDeleteBulkProject">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkCampaignForm">
        <div class="mt-20">
          <div class="justify-between">
            <div class="field-label mt-0">{{'INTEGRATION.campaign' | translate}}(s)</div>
            <label class="checkbox-container mb-4">
              <input type="checkbox" formControlName="shouldRemoveExistingCampaign">
              <span class="checkmark"></span>Remove Existing Campaign(s)
            </label>
          </div>
        </div>
        <form-errors-wrapper [control]="bulkCampaignForm.controls['campaign']" label="campaign">
          <ng-select [virtualScroll]="true" [items]="campaignList" *ngIf="!campaignListIsLoading else fieldLoader"
            ResizableDropdown [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
            (change)="trackingService.trackFeature('Web.Leads.Options.campaign.Click')" formControlName="campaign"
            class="bg-white">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </form-errors-wrapper>
      </form>
    </div>
    <div class="flex-center mt-20 mb-40" *ngIf="!bulkCampaignIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkAgency" data-automate-id="btnSaveBulkAgency"
        (click)="updateBulkCampaign('bulkCampaign')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>
<ng-template #BulkDeleteModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>Bulk {{ leadVisibilityEnum[appliedFilter?.leadVisibility] === 'Deleted' && bulkPermanentDelete? 'Restore' :
        'Delete' }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="bulkDeleteModalRef.hide()"></div>
    </div>
    <div class="px-12">
      <div class="field-label mb-10">{{'LEADS.selected-lead' | translate}} -
        {{selectedLeads?.length}}
      </div>
      <div class="flex-column scrollbar max-h-100-176">
        <ng-container *ngFor="let lead of selectedLeads">
          <div class="flex-between p-12 fw-600 text-sm border-bottom text-secondary bg-white">
            <span class="text-truncate-1 break-all">{{ lead.name }}</span>
            <div (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
              id="clkBulkDelete" data-automate-id="clkBulkDelete">
              <span class="icon ic-cancel m-auto ic-xx-xs"></span>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="flex-center" *ngIf="(!bulkDeleteIsLoading && !bulkRestoreIsLoading) else ratLoader">
        <button class="btn-coal mt-20" (click)="updateBulkDelete()">{{ (
          leadVisibilityEnum[appliedFilter?.leadVisibility] === 'Deleted' &&
          bulkPermanentDelete ?
          'BUTTONS.restore' : 'BUTTONS.delete') | translate }}</button>
      </div>
    </div>
  </div>
</ng-template>


<ng-template #fieldLoader>
  <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
</ng-template>

<ng-template #ratLoader>
  <div class="flex-center w-100 my-8">
    <img src="assets/images/loader-rat.svg" class="rat-loader h-20px w-20px" alt="loader">
  </div>
</ng-template>

<ng-template #trackerInfoModal>
  <h5 class="px-20 py-16 fw-semi-bold bg-coal text-white">{{ type }} </h5>
  <div class="p-20 flex-center-col">
    <h4 class="text-black-100 fw-600 mb-10 text-center word-break line-break">{{type}} in progress
    </h4>
    <h5 class="text-black-100 fw-semi-bold text-center word-break line-break">You can check
      <span class="cursor-pointer text-accent-green header-3 fw-600" (click)="openBulkStatus()">“Bulk
        Operation
        Tracker”</span> to view status
    </h5>
    <button class="btn-green mt-30" (click)="modalService.hide()">
      {{'BULK_LEAD.got-it' | translate}}</button>
  </div>
</ng-template>