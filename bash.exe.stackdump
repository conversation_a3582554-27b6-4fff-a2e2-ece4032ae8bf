Stack trace:
Frame         Function      Args
0007FFFFAA90  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9990) msys-2.0.dll+0x1FE8E
0007FFFFAA90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAD68) msys-2.0.dll+0x67F9
0007FFFFAA90  000210046832 (000210286019, 0007FFFFA948, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAA90  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAA90  000210068E24 (0007FFFFAAA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAD70  00021006A225 (0007FFFFAAA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF968FA0000 ntdll.dll
7FF967A50000 KERNEL32.DLL
7FF9664C0000 KERNELBASE.dll
7FF9685C0000 USER32.dll
7FF966490000 win32u.dll
7FF967A20000 GDI32.dll
7FF966B40000 gdi32full.dll
7FF966A90000 msvcp_win.dll
7FF966940000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF968E80000 advapi32.dll
7FF967ED0000 msvcrt.dll
7FF967970000 sechost.dll
7FF967CD0000 RPCRT4.dll
7FF9656F0000 CRYPTBASE.DLL
7FF966270000 bcryptPrimitives.dll
7FF967B20000 IMM32.DLL
