<div class="position-relative">
    <div class="px-20">
        <div class="scrollbar ph-h-100-350" *ngIf="!isNoteLoading else ratLoader"
            [ngClass]="whatsAppComp ? 'h-100-448' : 'h-100-310'">
            <ng-container *ngIf="hasNotesHistory;else noNotes">
                <ng-container *ngFor="let notesList of notesHistory">
                    <div *ngIf="notesList.data.length" class="border-bottom-slate justify-center">
                        <div *ngIf="notesList.data.length" class="text-gray text-xs fw-semi-bold mt-20 text-nowrap">
                            {{ notesList.date }}</div>
                        <div class="w-100">
                            <ng-container *ngFor="let note of notesList.data">
                                <div *ngIf="note.newValue != ''" class="card p-10 br-10 border-0 bg-secondary">
                                    <div class="text-accent-green">{{ note.updatedBy }}</div>
                                    <div class="align-center ml-4">
                                        <div [innerHTML]="convertUrlsToLinks(note.newValue)"
                                            class="text-large word-break max-w-385 line-break"></div>
                                    </div>
                                    <div class="flex-end text-xxs fw-semi-bold">{{ getTimeZoneDate(note.updatedOn,
                                        userData?.timeZoneInfo?.baseUTcOffset, 'timeWithMeridiem')}}
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                    </div>
                </ng-container>
            </ng-container>
        </div>
        <ng-template #ratLoader>
            <div class="flex-center w-100 ph-h-100-350" [ngClass]="whatsAppComp ? 'h-100-448' : 'h-100-293'">
                <img src="assets/images/loader-rat.svg" class="rat-loader" alt="loader">
            </div>
        </ng-template>
    </div>
    <div [formGroup]="notesForm" *ngIf="canEditNotes">
        <div class="brtr-10 brtl-10 p-12 d-flex align-center bg-pearl w-100 position-absolute ph-bottom-50">
            <div class="d-flex flex-grow-1 notes no-validation">
                <textarea placeholder="Type here...." formControlName="notes" rows="2"
                    class="border-0 outline-0 br-10 input-field d-flex flex-grow-1 p-16 text-gray header-5 non-resizable scrollbar"
                    #notesInput></textarea>
            </div>
            <button (click)="postNotes()" type="submit"
                [disabled]="!notesForm.controls['notes'].value || notesForm.controls['notes'].invalid"
                class="bg-accent-green h-40 ml-8 px-16 br-10 border-0" id="btnUpdateNotes"
                data-automate-id="btnUpdateNotes">
                <span class="ic-tick icon"></span>
            </button>
        </div>
    </div>

</div>
<ng-template #noNotes>
    <div class="h-100-393 flex-center-col">
        <ng-lottie [options]='options'></ng-lottie>
        <div class="fw-600 header-4 text-light-slate">No Notes Found</div>
    </div>
</ng-template>