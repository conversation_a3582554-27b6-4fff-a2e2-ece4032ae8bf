import { Action } from "@ngrx/store";

export enum AutoDialerActionTypes {
    FETCH_AUTO_DIALER = '[Auto Dialer] Fetch Auto Dialer',
    FETCH_AUTO_DIALER_SUCCESS = '[Auto Dialer] Fetch Auto Dialer Success',
    FETCH_STATUS_COUNT = '[Auto Dialer] Fetch Status Count',
    FETCH_STATUS_COUNT_SUCCESS = '[Auto Dialer] Fetch Status Count Success',
    UPDATE_USER_STATUS = '[Auto Dialer] Update User Status',
    UPDATE_USER_STATUS_SUCCESS = '[Auto Dialer] Update User Status Success',
    GET_USER_STATUS = '[Auto Dialer] Get User Status',
    GET_USER_STATUS_SUCCESS = '[Auto Dialer] Get User Status Success',
    ADD_LEAD = '[Auto Dialer] Add Lead',
    ADD_LEAD_SUCCESS = '[Auto Dialer] Add Lead Success',
    UPDATE_FILTER_PAYLOAD = '[Auto Dialer] Update Filter Payload',
}

export class FetchAutoDialer implements Action {
    readonly type: string = AutoDialerActionTypes.FETCH_AUTO_DIALER;
    constructor(public payload?: any) { }
}

export class FetchAutoDialerSuccess implements Action {
    readonly type: string = AutoDialerActionTypes.FETCH_AUTO_DIALER_SUCCESS;
    constructor(public response: any) { }
}

export class FetchStatusCount implements Action {
    readonly type: string = AutoDialerActionTypes.FETCH_STATUS_COUNT;
    constructor() { }
}

export class FetchStatusCountSuccess implements Action {
    readonly type: string = AutoDialerActionTypes.FETCH_STATUS_COUNT_SUCCESS;
    constructor(public response: any) { }
}

export class UpdateUserStatus implements Action {
    readonly type: string = AutoDialerActionTypes.UPDATE_USER_STATUS;
    constructor() { }
}

export class UpdateUserStatusSuccess implements Action {
    readonly type: string = AutoDialerActionTypes.UPDATE_USER_STATUS_SUCCESS;
    constructor() { }
}

export class GetUserStatus implements Action {
    readonly type: string = AutoDialerActionTypes.GET_USER_STATUS;
    constructor() { }
}

export class GetUserStatusSuccess implements Action {
    readonly type: string = AutoDialerActionTypes.GET_USER_STATUS_SUCCESS;
    constructor(public response: any) { }
}

export class DialerAddLead implements Action {
    readonly type: string = AutoDialerActionTypes.ADD_LEAD;
    constructor(public payload: any) { }
}

export class DialerAddLeadSuccess implements Action {
    readonly type: string = AutoDialerActionTypes.ADD_LEAD_SUCCESS;
    constructor(public response: any) { }
}

export class UpdateFilterPayload implements Action {
    readonly type: string = AutoDialerActionTypes.UPDATE_FILTER_PAYLOAD;
    constructor(public filter: any) { }
}