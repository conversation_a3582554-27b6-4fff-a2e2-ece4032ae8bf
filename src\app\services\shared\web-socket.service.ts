import { Injectable } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { Subject } from 'rxjs';
import { WAWrapperDto } from 'src/app/core/interfaces/leads.interface';
import { getTenantName } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';

// Single DTO for both frontend → backend and backend → frontend
export interface AutoDialerLeadDto {
  id?: string;
  name?: string;
  assignTo?: string;
  secondaryUserId?: string;
  callStartTime?: Date;
}

@Injectable({
  providedIn: 'root',
})
export class SignalRService {
  // Chat hub connection
  private chatHubConnection: signalR.HubConnection;
  private messageReceived = new Subject<WAWrapperDto>();
  // Dialer hub connection
  private dialerHubConnection: signalR.HubConnection;
  private dialerEventReceived = new Subject<AutoDialerLeadDto>();

  tenant: string = getTenantName();
  whatsAppBaseURL: string = env.whatsAppBaseURL;
  baseUrl: string = env.baseURL;

  constructor() { }

  public startConnection = () => {
    this.startChatConnection();
  };

  public startChatConnection = () => {
    this.chatHubConnection = new signalR.HubConnectionBuilder()
      .withUrl(`${this.whatsAppBaseURL}chat-hub`, {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
      })
      .withAutomaticReconnect()
      .build();

    this.chatHubConnection
      .start()
      .then(() => {
        console.log('SignalR Chat Hub connection started');
        this.registerChatEventListeners();
        this.joinChatGroup();
      })
      .catch((err: any) =>
        console.error('Error while starting SignalR Chat Hub connection: ' + err)
      );
  };

  public startDialerConnection = () => {
    this.dialerHubConnection = new signalR.HubConnectionBuilder()
      .withUrl(`${this.baseUrl}dialer-hub`, {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
      })
      .withAutomaticReconnect()
      .build();

    this.dialerHubConnection
      .start()
      .then(() => {
        console.log('SignalR Dialer Hub connection started');
        this.registerDialerEventListeners();
        this.joinDialerGroup();
      })
      .catch((err: any) =>
        console.error('Error while starting SignalR Dialer Hub connection: ' + err)
      );
  };

  private registerChatEventListeners = () => {
    const eventHandlers: { [event: string]: (...args: any[]) => void } = {
      ReceiveMessageAsync: (message: WAWrapperDto) =>
        this.messageReceived.next(message),
      ReceiveMessage: (message: WAWrapperDto) =>
        this.messageReceived.next(message),
      RegisterForGroupJoinAsync: (groupName: string) =>
        console.log(`Client joined chat group: ${groupName}`),
      // 'SendMessageAsync': (message: WAWrapperDto) => console.log(`Message sent to all clients: ${message}`),
      // 'RequestClientDisconnect': () => console.log('Request to disconnect received'),
      // 'SendMessageToCallerAsync': (message: WAWrapperDto) => console.log(`Message sent to caller: ${message}`),
      // 'SendMessageToGroupAsync': (message: string, groupName: string) => console.log(`Message sent to group ${groupName}: ${message}`),
    };

    for (const event in eventHandlers) {
      this.chatHubConnection.on(event, eventHandlers[event]);
    }
  };

  private registerDialerEventListeners = () => {
    const eventHandlers: { [event: string]: (...args: any[]) => void } = {
      ReceiveDialerEventAsync: (event: AutoDialerLeadDto) =>
        this.dialerEventReceived.next(event),
      ReceiveDialerEvent: (event: AutoDialerLeadDto) =>
        this.dialerEventReceived.next(event),
      CallStatusUpdated: (event: AutoDialerLeadDto) =>
        this.dialerEventReceived.next(event),
      LeadAssigned: (event: AutoDialerLeadDto) =>
        this.dialerEventReceived.next(event),
      UserStatusChanged: (event: AutoDialerLeadDto) =>
        this.dialerEventReceived.next(event),
      RegisterForDialerGroupJoinAsync: (groupName: string) =>
        console.log(`Client joined dialer group: ${groupName}`),
    };

    for (const event in eventHandlers) {
      this.dialerHubConnection.on(event, eventHandlers[event]);
    }
  };

  private registerEventListeners = () => {
    this.registerChatEventListeners();
  };

  private checkChatConnection = (callback: () => void) => {
    if (this.chatHubConnection && this.chatHubConnection.state === signalR.HubConnectionState.Connected) {
      callback();
    } else {
      console.error('Chat Hub connection is not in the Connected state.');
    }
  };

  private checkDialerConnection = (callback: () => void) => {
    if (this.dialerHubConnection && this.dialerHubConnection.state === signalR.HubConnectionState.Connected) {
      callback();
    } else {
      console.error('Dialer Hub connection is not in the Connected state.');
    }
  };

  // Legacy method for backward compatibility
  private checkConnection = (callback: () => void) => {
    this.checkChatConnection(callback);
  };

  public stopConnection = () => {
    this.stopChatConnection();
    this.stopDialerConnection();
  };

  public stopChatConnection = () => {
    if (this.chatHubConnection) {
      this.chatHubConnection
        .stop()
        .then(() => console.log('SignalR Chat Hub connection stopped'))
        .catch((err: any) =>
          console.error('Error while stopping SignalR Chat Hub connection: ' + err)
        );
    }
  };

  public stopDialerConnection = () => {
    if (this.dialerHubConnection) {
      this.dialerHubConnection
        .stop()
        .then(() => console.log('SignalR Dialer Hub connection stopped'))
        .catch((err: any) =>
          console.error('Error while stopping SignalR Dialer Hub connection: ' + err)
        );
    }
  };

  public joinChatGroup = () => {
    this.checkChatConnection(() => {
      this.chatHubConnection
        .invoke('RequestForGroupJoinAsync', this.tenant)
        .then(() => console.log('Request for chat group join sent'))
        .catch((err: any) =>
          console.error('Error sending request for chat group join', err)
        );
    });
  };

  public joinDialerGroup = () => {
    this.checkDialerConnection(() => {
      this.dialerHubConnection
        .invoke('RequestForGroupJoinAsync', this.tenant)
        .then(() => console.log('Request for dialer group join sent'))
        .catch((err: any) =>
          console.error('Error sending request for dialer group join', err)
        );
    });
  };

  public joinGroup = () => {
    this.joinChatGroup();
  };

  public leaveChatGroup = (groupName: string) => {
    this.checkChatConnection(() => {
      this.chatHubConnection
        .invoke('LeaveGroup', groupName)
        .then(() => console.log(`Left chat group ${groupName}`))
        .catch((err: any) => console.error(`Error leaving chat group ${groupName}`, err));
    });
  };

  public leaveDialerGroup = (groupName: string) => {
    this.checkDialerConnection(() => {
      this.dialerHubConnection
        .invoke('LeaveGroup', groupName)
        .then(() => console.log(`Left dialer group ${groupName}`))
        .catch((err: any) => console.error(`Error leaving dialer group ${groupName}`, err));
    });
  };

  public leaveGroup = (groupName: string) => {
    this.leaveChatGroup(groupName);
  };

  public sendMessageToGroup = (
    groupName: string,
    waWrapperDto: WAWrapperDto
  ) => {
    this.checkChatConnection(() => {
      this.chatHubConnection
        .invoke('SendMessageToGroupAsync', groupName, waWrapperDto)
        .then(() => console.log('Message sent to chat group'))
        .catch((err: any) => console.error('Error sending message to chat group', err));
    });
  };

  public sendDialerEventToGroup = (
    groupName: string,
    dialerEvent: AutoDialerLeadDto
  ) => {
    this.checkDialerConnection(() => {
      this.dialerHubConnection
        .invoke('SendDialerEventToGroupAsync', groupName, dialerEvent)
        .then(() => console.log('Dialer event sent to group'))
        .catch((err: any) => console.error('Error sending dialer event to group', err));
    });
  };

  // Simplified method using only AutoDialerLeadDto
  public sendDialerEvent = (
    loginUserId: string,
    leadId?: string,
    callStartTime?: Date | null,
    leadName?: string,
    secondaryUserId?: string
  ) => {
    const dialerEvent: AutoDialerLeadDto = {
      id: leadId,
      name: leadName,
      assignTo: loginUserId,
      secondaryUserId: secondaryUserId,
      callStartTime: callStartTime
    };

    this.sendDialerEventToGroup(this.tenant, dialerEvent);
  };

  // Get chat message listener
  public getMessageListener = () => {
    return this.messageReceived.asObservable();
  };

  // Get dialer event listener
  public getDialerEventListener = () => {
    return this.dialerEventReceived.asObservable();
  };
}
