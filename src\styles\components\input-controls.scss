.ng-select {
  padding: 2px 12px !important;
  @extend .border-gray, .fw-600, .bg-light-slate, .br-5;

  .ng-select-container {
    min-height: 36px !important;
    align-items: center;

    .ng-value-container {
      @extend .border-top-0, .p-0;
      align-items: center;

      .ng-input {
        bottom: unset;
      }

      .ng-placeholder {
        @extend .fw-semi-bold, .text-sm, .text-light-slate, .mr-16, .word-break;
      }
    }
  }

  &.ng-select-multiple .ng-select-container {
    .ng-clear-wrapper {
      border-top: 0px !important;
    }

    .ng-arrow-wrapper {
      border-top: 0px !important;
    }

    .ng-value-container {
      @extend .scrollbar, .scroll-hide;
      max-height: 36px;

      .ng-value {
        @extend .text-gray, .br-5, .bg-pearl, .m-4;
        white-space: unset !important;
        word-break: break-word !important;

        .ng-value-icon {
          @extend .text-gray;

          &:hover {
            color: $primary-black !important;
          }
        }
      }

      &.ng-has-value .ng-arrow-wrapper {
        border-top: unset;
      }
    }
  }

  .ng-option.ng-option-disabled {
    @extend .pe-none;
  }
}

.ng-select {
  &.ng-select-disabled {
    .ng-select-container {
      cursor: default !important;

      .ng-value-container .ng-input>input {
        cursor: default !important;
      }
    }
  }

  .ng-select-container {
    cursor: pointer !important;

    .ng-value-container .ng-input>input {
      cursor: pointer !important;
    }
  }
}

.ng-select .ng-select-container .ng-value-container .ng-input>input {
  padding: unset !important;
}

.ng-select .ng-arrow-wrapper .ng-arrow,
.ng-select.ng-select-focused .ng-select-container .ng-arrow-wrapper .ng-arrow,
.ng-select .ng-select-container {
  @extend .text-coal;
}

.ng-select .ng-has-value .ng-placeholder,
.ng-select.ng-select-opened .ng-placeholder,
.ng-select.ng-select-focused .ng-select-container .ng-value-container .ng-placeholder,
.ng-select-container::after {
  @extend .d-none;
}

.ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
  align-self: flex-end;
  bottom: 11px;
}

.ng-dropdown-panel {
  @extend .mt-16, .p-4, .br-10;

  .ng-dropdown-panel-items {
    .ng-option {
      @extend .px-8;

      &.ng-option-selected,
      &:hover {
        @extend .bg-light-pearl, .text-coal, .fw-400, .br-4;
      }
    }
  }

  .scroll-host {
    @extend .scrollbar;
  }
}

.ng-select-multiple .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  @extend .p-8;
}

.ng-select.ng-select-single .ng-select-container .ng-value-container,
.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
  white-space: unset !important;
  max-height: 15px !important;
  word-break: break-all !important;
}

.dashboard-dropdown {
  .ng-dropdown-panel .ng-dropdown-panel-items {
    max-height: 150px;
  }
}

.datefilter-scroll {
  .ng-dropdown-panel .ng-dropdown-panel-items {
    max-height: 300px !important;
  }
}

.ng-dropdown-sm {
  .ng-dropdown-panel .ng-dropdown-panel-items {
    max-height: 100px !important;
  }
}

.manage-select {
  .ng-select.ng-select-single .ng-select-container .ng-clear-wrapper {
    align-self: flex-end;
    bottom: 5px !important;
  }

  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    align-self: flex-end;
    bottom: 9px !important;
  }

  .ng-clear-wrapper {
    @extend .d-none;
  }
}

.manage-dropdown {
  .ng-clear-wrapper {
    @extend .d-none;
  }
}

.org-profile {
  .ng-select.ng-select-single .ng-select-container .ng-clear-wrapper {
    bottom: 0 !important;
  }
}

.show-dropdown {
  @extend .no-validation;

  .ng-select {
    @extend .bg-white, .text-xs, .border-0;

    .ng-select-container {
      min-height: 23px !important;
    }

    &.ng-select-single .ng-select-container .ng-arrow-wrapper {
      align-self: unset;
      bottom: unset;
    }
  }

  .ng-select .ng-arrow-wrapper .ng-arrow,
  .ng-select .ng-select-container .ng-value-container .ng-placeholder,
  .ng-select .ng-select-container {
    @extend .text-light-gray;
  }

  .ng-select .ng-clear-wrapper {
    @extend .d-none;
  }
}

.ng-select-sm {
  .ng-select {
    padding: 0px 12px !important;

    .ng-select-container {
      min-height: 32px !important;
    }

    .ng-value-container {
      @extend .scrollbar, .scroll-hide;
      max-height: 32px !important;
    }

    .ng-value {
      @extend .bg-white;
    }

    &.ng-select-single {
      .ng-value {
        background-color: $dark-500 !important;
      }
    }
  }

  .ng-select.bg-white {
    &.ng-select-single {
      .ng-value {
        background-color: $white !important;
      }
    }
  }
}

.ng-select-xs {
  .ng-select {
    padding: 0px 12px !important;

    .ng-select-container {
      min-height: 24px !important;
    }

    .ng-value-container {
      @extend .scrollbar, .scroll-hide;
      max-height: 24px !important;
    }

    .ng-value {
      background-color: unset !important;
    }

    .ng-arrow-wrapper {
      align-self: flex-end;
      bottom: 6px !important;
    }
  }

  .ng-select.ng-select-single .ng-select-container .ng-clear-wrapper {
    bottom: 2px;
  }
}

.dashboard {
  .ng-select {
    border: 0.2px solid $slate-40 !important;
    background-color: $black-800 !important;
    padding: 4px !important;
    border-radius: 2px !important;

    .ng-select-container {
      min-height: 20px !important;
      align-items: center;
    }

    .ng-value-container {
      @extend .fw-semi-bold, .text-xs;
      max-height: 20px !important;
    }

    .ng-value {
      background-color: $black-800 !important;
      color: $white;
    }

    ng-dropdown-panel {
      // @extend .ph-nleft-65;
      // width: 165px !important;
    }
  }

  .ng-arrow-wrapper {
    margin-left: 8px !important;
    bottom: 3px !important;
    border-top: 3px solid transparent !important;

    .ng-arrow {
      color: white !important;
    }
  }
}

.lead-dropdown {
  .ng-select {
    padding: 2px 0px !important;
    border: none !important;
    background-color: white !important;

    .ng-select-container {
      align-items: center;
    }

    .ng-dropdown-panel {
      width: 190px;
    }
  }
}

.listing-dropdown {
  .ng-select {
    padding: 2px 0px !important;
    border: none !important;
    background-color: white !important;

    .ng-select-container {
      align-items: center;
    }

    .ng-value-container {
      margin-bottom: 8px !important;
    }

    .ng-arrow-wrapper {
      margin-bottom: 4px !important;
      margin-right: 8px !important;
    }

    .ng-option-selected,
    .ng-option-marked {
      background-color: $black-10 !important;
      color: white !important;
    }

    .ng-value-label {
      margin-left: 16px !important;
    }

    .ng-dropdown-panel {
      width: 100px;
    }
  }
}

.ng-select-w-171 {
  .ng-dropdown-panel {
    width: 171px;
  }
}

.dashboard-input {
  .ng-select.ng-select-multiple .ng-select-container .ng-value-container {
    max-height: 12px !important;
  }

  .ng-value-label {
    @extend .text-white;
    font-size: 9px !important;
    line-height: 12px !important;
  }

  .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
    margin: 0 0 4px !important;
    background-color: $white !important;
    border-radius: 6px !important;
    padding: 0px 4px !important;
  }

  .ng-value-label {
    color: $black-800 !important;
    @extend .fw-semi-bold;
  }

  .ng-placeholder {
    @extend .text-white;
  }

  .ng-select input {
    color: $black-800;
  }

  .ng-select .ng-clear-wrapper {
    color: $white;
  }

  .ng-select.ng-select-multiple .ng-select-container.ng-has-value .ng-clear-wrapper {
    border-top: unset;
  }
}

.ng-select-sm-gray {
  @extend .ng-select-sm;

  .ng-select .ng-value {
    background-color: $slate-200 !important;
  }
}

.ng-select-gray {
  &.ng-select .ng-value {
    background-color: $white !important;
  }
}

.dashboard-input-black {
  @extend .dashboard-input;

  .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
    @extend .d-none;
  }

  .ng-select input {
    color: $black-800 !important;
  }

  .ng-select .ng-clear-wrapper {
    @extend .d-none;
  }

  .ng-arrow-wrapper {
    bottom: 1px !important;
  }
}

// .dashboard-input-hide {
//   .ng-select input {
//     color: $violet-600 !important;
//     position: absolute;
//     left: -100px; 
//   }
// }

.reminder {
  ng-dropdown-panel {
    border: 1px solid $accent-green !important;
    background-color: $green-350 !important;
    padding: 0px !important;
    margin: 4px !important;
  }

  .ng-option-marked {
    background-color: $accent-green !important;
  }

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    border-radius: 8px !important;
  }

  .ng-select {
    border: 0.2px solid $slate-40 !important;
    background-color: $black-10 !important;
    padding: 2px 10px !important;
    border-radius: 10px !important;

    .ng-select-container {
      min-height: 20px !important;
      align-items: center;
    }

    .ng-value {
      background-color: $black-10 !important;
      color: $white;
    }
  }

  .ng-arrow-wrapper {
    .ng-arrow {
      color: $white !important;
      top: 7px;
    }
  }
}

.show-dropdown-gray {
  @extend .no-validation;

  .ng-select {
    @extend .text-xs, .border-0;
    padding: 5px 12px !important;

    .ng-select-container {
      min-height: 23px !important;
    }

    &.ng-select-single .ng-select-container .ng-arrow-wrapper {
      align-self: unset;
      bottom: unset;
    }
  }

  .ng-select .ng-arrow-wrapper .ng-arrow,
  .ng-select .ng-select-container .ng-value-container .ng-placeholder,
  .ng-select .ng-select-container {
    @extend .text-black;
  }
}

.show-dropdown-white {
  @extend .show-dropdown-gray;

  .ng-select {
    font-size: 11px !important;
    background-color: $white;
  }

  .ng-select .ng-select-container .ng-value-container .ng-placeholder {
    font-size: 11px !important;
  }

  .ng-select .ng-select-container .ng-value-container {
    justify-content: flex-end;
  }

  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    margin-left: 8px;
    bottom: 2px;
  }

  .ng-select .ng-clear-wrapper {
    @extend .d-none;
  }
}

.show-hide-dropdown {
  @extend .show-dropdown;

  .ng-select.ng-select-multiple .ng-select-container.ng-has-value {
    align-items: flex-end !important;
    overflow: hidden;
    height: 20px;

    .ng-arrow-wrapper {
      bottom: 5px;
    }
  }

  .ng-select-opened .ng-select-container .ng-arrow-wrapper {
    bottom: 0px;
  }

  .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-label {
    font-size: 10px;
  }
}

.show-hide-gray {
  @extend .show-dropdown-gray;

  .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
    @extend .bg-white, .d-none;
  }

  .ng-select.ng-select-multiple .ng-select-container.ng-has-value {
    align-items: flex-end !important;
    overflow: hidden;
    height: 20px;

    .ng-arrow-wrapper {
      bottom: 5px;
    }
  }

  .ng-select-opened .ng-select-container .ng-arrow-wrapper {
    bottom: 0px;
  }

  .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-label {
    font-size: 10px;
  }

  .ng-select .ng-clear-wrapper {
    @extend .d-none;
  }
}

.show-hide-slate {
  @extend .show-hide-gray;

  .ng-select {
    padding: 1px 5px !important;
    background-color: $violet-600 !important;
    border-radius: 0px !important;

    ng-dropdown-panel {
      width: 150px !important;
      left: -60px;
    }

    .ng-arrow-wrapper .ng-arrow {
      color: $black-200 !important;
    }
  }
}

.small-dropdown {
  ng-select {
    @extend .bg-white;
    font-weight: 400 !important;
  }

  .ng-select .ng-select-container {
    min-height: 23px !important;
  }

  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    bottom: 4px;
  }

  .ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value {
    color: $primary-black;
  }
}

.black-dropdown {
  @extend .small-dropdown;

  ng-select {
    @extend .bg-linear-navy, .border-0, .p-0, .mb-8;
  }

  .ng-select .ng-arrow-wrapper .ng-arrow,
  .ng-select.ng-select-focused .ng-select-container .ng-arrow-wrapper .ng-arrow,
  .ng-select .ng-select-container {
    @extend .text-white;
  }
}

.image-dropdown {
  ng-select {
    @extend .bg-black-6, .text-xs;
    border-radius: 0px 0px 5px 5px !important;
    border: unset;
    padding: 0px 12px !important;
  }

  .ng-select .ng-arrow-wrapper .ng-arrow,
  .ng-select .ng-select-container .ng-value-container .ng-placeholder,
  .ng-select .ng-select-container .ng-value-container .ng-input>input,
  .ng-select .ng-select-container {
    @extend .text-white;
  }

  .ng-select .ng-select-container {
    min-height: 23px !important;
  }

  .ng-select .ng-select-container .ng-value-container {
    border-top: unset;
  }

  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    bottom: 7px !important;
  }
}

.field-tag {
  @extend .position-relative;

  ng-select {
    padding-left: 45px !important;
  }

  .ng-select .ng-arrow-wrapper .ng-arrow,
  .ng-select.ng-select-focused .ng-select-container .ng-arrow-wrapper .ng-arrow {
    @extend .d-none;
  }

  .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
    width: 90%;

    input {
      @extend .text-truncate;
    }
  }

  .search {
    @extend .border-gray,
    .position-absolute,
    .top-0,
    .left-0,
    .br-5,
    .bg-light-slate;
    margin: 3px;
    padding: 17px;
  }
}

ngx-mat-intl-tel-input,
ng-select,
input,
textarea {
  @extend .border-gray;

  &.ng-touched {
    &:not(.ng-valid) {
      @extend .border-gray;
    }

    &.ng-valid {
      @extend .border-gray;
    }

    &.ng-invalid {
      border: 1px solid $red-800;
    }
  }

  &.ng-dirty {
    &:not(.ng-valid) {
      @extend .border-gray;
    }

    &.ng-valid {
      border: 1px solid $green-600 !important;
    }

    &.ng-invalid {
      border: 1px solid $red-800;
    }
  }

  &.ng-touched:not([value=""]) {
    @extend .border-gray;

    &.ng-invalid {
      border: 1px solid $red-800;
    }
  }
}

ngx-mat-intl-tel-input {
  display: block;
  @extend .br-6;

  .ngx-mat-tel-input-container {
    @extend .br-6;
  }

  button {
    @extend .brtl-6, .brbl-6;
    opacity: 1 !important;
    height: 38.5px !important;
    top: 1px !important;
    left: 1px !important;
    border-right: 1px solid $dark-400 !important;
  }

  .mat-input-element {
    @extend .bg-white;
    padding-left: 100px !important;
  }
}

.no-number-input .ngx-mat-tel-input-container {
  @extend .h-40, .br-4;

  button {
    @extend .w-100;
    border-right: unset !important;
  }
}

.no-validation {

  ng-select.ng-dirty.ng-valid,
  input.ng-dirty.ng-valid,
  textarea.ng-dirty.ng-valid {
    border: unset !important;
  }
}

.no-input-validation {

  input.ng-dirty.ng-valid,
  input.ng-dirty.ng-valid,
  textarea.ng-dirty.ng-valid {
    border: 1px solid $dark-400 !important;
  }
}

.profile {
  @extend .position-relative;

  input,
  textarea {
    border: none;
    padding: 4px 12px !important;
  }

  .ngx-mat-tel-input-container {
    input.mat-input-element {
      padding-left: 68px !important;
    }

    button.mat-menu-trigger {
      width: min-content;

      .mat-button-wrapper {
        .country-selector-flag {
          display: none;
        }
      }

      height: 23px !important;
    }
  }

  .ng-select {
    padding: 0px 12px !important;
  }

  .ng-select.ng-select-disabled .ng-select-container .ng-arrow-wrapper {
    display: none;
  }

  .editable {
    @extend .border-gray;
    border-radius: 5px;
  }
}

.btn-full-dropdown {
  @extend .no-validation, .text-white, .position-relative;

  .ng-select {
    background-color: $primary-black;
    padding: 0px 6px !important;
    border: none !important;
  }

  .ng-select-container {
    min-height: 32px !important;
  }

  .ng-arrow-wrapper {
    .ng-arrow {
      color: $white !important;
    }
  }

  .ng-dropdown-panel {
    width: 130px !important;
    left: unset;
    right: 0px;
  }

  .ng-select .ng-clear-wrapper .ng-clear {
    display: none !important;
  }

  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    bottom: 9px !important;
  }

  &.btn-w-100 {
    @extend .w-100px, .ip-w-50px;
  }
}

.btn-right-dropdown {
  @extend .btn-full-dropdown;

  .ng-select {
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
  }

  &.btn-w-30 {
    @extend .w-30px;
  }

  &.black-100 {
    .ng-select {
      background-color: $black-100 !important;
    }
  }
}

.btn-left-dropdown {
  @extend .btn,
  .btn-sm,
  .flex-center,
  .bg-coal,
  .text-light,
  .px-8,
  .py-12,
  .no-validation,
  .brtr-0,
  .brbr-0;
}

.dropdown-black {
  .ng-dropdown-panel {
    width: 140px !important;
    left: -108px !important;
    @extend .bg-black-100;
  }

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
    @extend .bg-black-100;
  }
}

.user {
  .ng-select {
    background-color: unset;
    border: none !important;
    padding: 0 !important;
    @extend .text-decoration-underline;
  }
}

input.toggle-switch[type="checkbox"] {
  width: 32px;
  height: 20px;
  opacity: 0;
  z-index: 1;
  cursor: pointer;
  position: absolute;

  +.switch-label {
    position: relative;
    display: inline-block;
    min-height: 20px;
    cursor: pointer;
    text-align: left;
    @extend .m-0;
    @extend .pl-40;
  }

  +.switch-label:before,
  +.switch-label:after {
    content: "";
    position: absolute;
    margin: 0;
    outline: 0;
    top: 50%;
    -ms-transform: translate(0, -50%);
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border: none;
  }

  +.switch-label:before {
    left: 1px;
    width: 32px;
    height: 20px;
    border-radius: 8px;
    background-color: $slate-700 !important;
  }

  +.switch-label:after {
    left: 4px;
    width: 32px;
    height: 20px;
    background-color: $white !important;
    border-radius: 40px;
  }

  &:checked+.switch-label:before {
    background-color: $green-750 !important;
  }

  &.toggle-blue {

    +.switch-label:before,
    +.switch-label:after {
      border: 3px solid $blue-500;
    }

    +.switch-label:before {
      width: 11px;
      background-color: $white !important;
    }

    +.switch-label:after {
      width: 11px;
      height: 11px;
      background-color: $blue-500 !important;
    }

    &:checked+.switch-label:before {
      background-color: $white !important;
      border: 3px solid $blue-500;
    }
  }

  &.toggle-green {
    +.switch-label:after {
      background-color: $white !important;
    }

    +.switch-label:before {
      background-color: $green-750 !important;
    }

    &:checked+.switch-label:before {
      background-color: $green-750 !important;
    }
  }

  &.toggle-bothsides {
    +.switch-label:after {
      background-color: $accent-green !important;
    }

    &:checked+.switch-label:before {
      background-color: $slate-700 !important;
    }
  }

  &.toggle-active-sold {
    +.switch-label:after {
      width: 11px;
      height: 11px;
      border: 0.6px solid $white;
      box-shadow: 1.6px 0.8px 3.2px $red-150, inset 0.8px 0.8px 3.2px $red-250;
    }

    +.switch-label:before {
      @extend .d-flex;
      @extend .align-items-center;
      @extend .justify-content-end;
      font-size: 8px;
      color: $white;
      background-color: $pink-150 !important;
      width: 40px;
      height: 20px;
      border-radius: 10px;
    }

    &:checked+.switch-label:before {
      @extend .d-flex;
      @extend .align-items-center;
      @extend .justify-content-start;
      background-color: $green-750 !important;
    }

    &:checked+.switch-label:after {
      -ms-transform: translate(200%, -50%);
      -webkit-transform: translate(200%, -50%);
      transform: translate(200%, -50%);
      box-shadow: -1.6px 0.8px 3.2px $green-850,
        inset -0.8px -0.8px 3.2px $green-950;
    }
  }

  &.toggle-availability {
    +.switch-label {
      @extend .pl-70;
      min-height: 30px;
    }

    +.switch-label:before {
      width: 60px;
      height: 30px;
      border-radius: 8px;
      background-color: #e5e5e5 !important;
      border: none;
    }

    +.switch-label:after {
      width: 26px;
      height: 26px;
      left: 2px;
      top: 2px;
      border-radius: 25%;
      transform: translate(0, 0);
      @extend .d-flex, .align-center, .justify-content-center;
      font-family: "leadRat" !important;
      color: #999;
      content: "\ea14"; // ic-briefcase icon code
    }

    &:checked+.switch-label:before {
      background-color: $green-750 !important;
    }

    &:checked+.switch-label:after {
      transform: translate(30px, 0);
      color: $green-750;
    }
  }

  +.switch-label .toggle-on {
    display: none;
  }

  +.switch-label .toggle-off {
    display: inline-block;
  }

  &:checked+.switch-label {
    .icon-ripple {
      left: 16px;
    }
  }

  &:checked+.switch-label:after {
    -ms-transform: translate(114%, -50%);
    -webkit-transform: translate(114%, -50%);
    transform: translate(114%, -50%);
  }

  &:checked+.switch-label .toggle-on {
    display: inline-block;
  }

  &:checked+.switch-label .toggle-off {
    display: none;
  }

  &:disabled~.switch-label:before {
    background: $white;
    pointer-events: none;
  }

  &:checked {
    +.switch-label {
      +.icons {
        .ic-close {
          @extend .invisible;
        }
      }
    }
  }

  &:not(:checked) {
    +.switch-label {
      +.icons {
        .ic-check {
          @extend .invisible;
        }
      }
    }
  }
}

// .custom-dd-with-disabled {
//   .ng-value-container {
//     .ng-value {}

//     .disabled-tag {
//       @extend .fw-semi-bold, .text-xxs;
//       color: $red-10;
//     }
//   }

//   .ng-dropdown-panel {
//     .ng-dropdown-panel-items {
//       .ng-option-disabled {
//         .disabled-tag {
//           @extend .error-message-custom, .top-10, .right-20;
//         }
//       }
//     }
//   }
// }

.d-dropdown {
  .ng-value-container {
    .value {
      @extend .align-center-col, .header-5, .fw-semi-bold;
    }

    .count {
      @extend .text-sm, .fw-700, .position-absolute, .mt-12, .right-30;
    }
  }

  .ng-option-label {
    .value {
      @extend .align-center;
    }
  }
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-selected {
  color: unset !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup {
  padding: 9px !important;
}

.budget-dropdown {
  .ng-select {
    padding: 0px 6px 10px !important;
    height: 34px;
    width: 60px;
    font-size: 12px;
    @extend .header-5, .fw-700;
  }
}

.adv-dropdown {
  .ng-select {
    padding: 0px 8px !important;
    font-size: 12px;
    height: 29px;
    width: 62px;
  }
}

.hide-dropdown-content {
  &.ng-select {
    cursor: default !important;

    .ng-select-container {
      cursor: default !important;
    }

    .ng-arrow-wrapper {
      cursor: default !important;
    }
  }

  .ng-dropdown-panel {
    display: none !important;
  }

  .ng-arrow {
    opacity: 0 !important;
  }
}

.error-text {
  @extend .text-error-red, .fw-semi-bold, .text-xxs, .text-nowrap;
}

.ng-value-label,
.ng-label {
  .text-disabled {
    @extend .position-absolute, .bottom-0, .right-45, .error-text;

    &.gray {
      @extend .text-dark-gray;
    }
  }
}

.ng-option-label,
.ng-option {
  .dropdown-position {
    @extend .flex-between, .position-relative;
  }

  .text-disabled {
    @extend .error-text, .position-relative;

    &.gray {
      @extend .text-dark-gray;
    }
  }
}