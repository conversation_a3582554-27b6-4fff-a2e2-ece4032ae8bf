import { Action, createSelector } from '@ngrx/store';
import { UserStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  AttendanceActionTypes,
  FetchAttendanceExportStatusSuccess,
  FetchAttendanceListByIdSuccess,
  FetchAttendanceListNoAuthSuccess,
  FetchAttendanceListSuccess,
  FetchAttendanceSettingSuccess,
  FetchNotificationSuccess,
  UpdateFilterPayload,
  UpdateFilterPayloadNoAuth,
} from './attendance.actions';

export type AttendanceState = {
  exportAttendanceStatus?: any;
  attendanceSettings?: any;
  notification?: any;
  attendanceList?: Array<any>;
  attendanceListNoAuth?: Array<any>;
  attendanceListById?: Array<any>;
  filtersPayload: any;
  filtersPayloadNoAuth: any;
  isAttendanceLoading: boolean;
  isExportAttendanceStatusLoading: boolean;
  isShiftUserLoading: boolean;
};

const initialState: AttendanceState = {
  attendanceList: [],
  attendanceListNoAuth: [],
  attendanceListById: [],
  exportAttendanceStatus: [],
  attendanceSettings: [],
  notification: [],

  filtersPayload: {
    pageNumber: 1,
    pageSize: 10,
    path: 'attendance',
    UserStatus: UserStatus.Active,
  },
  filtersPayloadNoAuth: {},
  isAttendanceLoading: true,
  isExportAttendanceStatusLoading: true,
  isShiftUserLoading: false,
};

export function attendanceReducer(
  state: AttendanceState = initialState,
  action: Action
): AttendanceState {
  switch (action.type) {
    case AttendanceActionTypes.FETCH_ATTENDANCE_LIST:
      return {
        ...state,
        isAttendanceLoading: true
      };
    case AttendanceActionTypes.FETCH_ATTENDANCE_LIST_SUCCESS:
      return {
        ...state,
        attendanceList: (action as FetchAttendanceListSuccess).response,
        isAttendanceLoading: false
      };
    case AttendanceActionTypes.FETCH_ATTENDANCE_LIST_NO_AUTH_SUCCESS:
      return {
        ...state,
        attendanceListNoAuth: (action as FetchAttendanceListNoAuthSuccess)
          .response,
      };
    case AttendanceActionTypes.FETCH_ATTENDANCE_LIST_BY_ID_SUCCESS:
      return {
        ...state,
        attendanceListById: (action as FetchAttendanceListByIdSuccess).response,
      };
    case AttendanceActionTypes.UPDATE_FILTER_PAYLOAD:
      return {
        ...state,
        filtersPayload: (action as UpdateFilterPayload).filter,
      };
    case AttendanceActionTypes.UPDATE_FILTER_PAYLOAD_NO_AUTH:
      return {
        ...state,
        filtersPayloadNoAuth: (action as UpdateFilterPayloadNoAuth).filter,
      };
    case AttendanceActionTypes.FETCH_ATTENDANCE_EXPORT_STATUS:
      return {
        ...state,
        isExportAttendanceStatusLoading: true,
      };
    case AttendanceActionTypes.FETCH_ATTENDANCE_EXPORT_STATUS_SUCCESS:
      return {
        ...state,
        exportAttendanceStatus: (action as FetchAttendanceExportStatusSuccess).response,
        isExportAttendanceStatusLoading: false
      };
    case AttendanceActionTypes.FETCH_ATTENDANCE_SETTING_SUCCESS:
      return {
        ...state,
        attendanceSettings: (action as FetchAttendanceSettingSuccess).response?.data,
      };
    case AttendanceActionTypes.FETCH_NOTIFICATION_SUCCESS:
      return {
        ...state,
        notification: (action as FetchNotificationSuccess).response?.data,
      };
    case AttendanceActionTypes.UPDATE_ATTENDANCE_SETTING:
      return {
        ...state,
        isShiftUserLoading: true,
      };
    case AttendanceActionTypes.UPDATE_ATTENDANCE_SETTING_SUCCESS:
      return {
        ...state,
        isShiftUserLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.attendance;

export const getAttendanceList = createSelector(
  selectFeature,
  (state: AttendanceState) => state.attendanceList
);

export const getAttendanceListNoAuth = createSelector(
  selectFeature,
  (state: AttendanceState) => state.attendanceListNoAuth
);

export const getFiltersPayload = createSelector(
  selectFeature,
  (state: AttendanceState) => {
    return state.filtersPayload;
  }
);

export const getFiltersPayloadNoAuth = createSelector(
  selectFeature,
  (state: AttendanceState) => {
    return state.filtersPayloadNoAuth;
  }
);

export const getAttendanceListById = createSelector(
  selectFeature,
  (state: AttendanceState) => state.attendanceListById
);

export const getExportAttendanceStatus = createSelector(
  selectFeature,
  (state: AttendanceState) => state.exportAttendanceStatus
);

export const getAttendanceSettings = createSelector(
  selectFeature,
  (state: AttendanceState) => state.attendanceSettings
);

export const getAttendanceSettingsIsloading = createSelector(
  selectFeature,
  (state: AttendanceState) => state.isShiftUserLoading
);

export const getNotification = createSelector(
  selectFeature,
  (state: AttendanceState) => state.notification
);

export const getAttendanceLoading = createSelector(
  selectFeature,
  (state: AttendanceState) => state.isAttendanceLoading
);

export const getIsExportAttendanceLoading = createSelector(
  selectFeature,
  (state: AttendanceState) => state.isExportAttendanceStatusLoading
);
